/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --secondary-color: #6c757d;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --border-color: #ddd;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--dark-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: 15px 25px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-brand h1 {
    color: var(--dark-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.version {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.nav-status {
    display: flex;
    align-items: center;
    gap: 15px;
}

.connection-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.status-text {
    font-weight: 600;
    font-size: 0.9rem;
}

.status-connected {
    background: var(--success-color);
    color: var(--success-color);
    animation: pulse-green 2s ease-in-out infinite;
}

.status-disconnected {
    background: var(--danger-color);
    color: var(--danger-color);
}

.status-connecting {
    background: var(--warning-color);
    color: var(--warning-color);
    animation: pulse-orange 1.5s ease-in-out infinite;
}

@keyframes pulse-green {
    0%, 100% { box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.3); }
    50% { box-shadow: 0 0 0 6px rgba(39, 174, 96, 0.1); }
}

@keyframes pulse-orange {
    0%, 100% { box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.3); }
    50% { box-shadow: 0 0 0 6px rgba(243, 156, 18, 0.1); }
}

/* 主要内容布局 */
.main-content {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 0;
}

.sidebar {
    width: 350px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

/* 面板样式 */
.panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.panel:hover {
    box-shadow: var(--shadow-medium);
}

.panel h2, .panel h3 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.full-height {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 按钮样式 */
.btn {
    padding: 10px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn:active {
    transform: translateY(0);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-block {
    width: 100%;
    margin-bottom: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--dark-color);
}

.btn-outline:hover {
    background: var(--light-color);
    border-color: var(--primary-color);
}

/* 快速操作面板 */
.quick-actions {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(155, 89, 182, 0.1) 100%);
    border: 2px solid rgba(52, 152, 219, 0.2);
}

.quick-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-counter {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
    color: var(--secondary-color);
    pointer-events: none;
}

.form-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 标签页样式 */
.tab-nav {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: 5px;
    margin-bottom: 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: none;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--secondary-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-light);
}

.tab-content {
    display: none;
    flex: 1;
    min-height: 0;
}

.tab-content.active {
    display: flex;
    flex-direction: column;
}

/* 客户端列表样式 */
.clients-container {
    margin-top: 10px;
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.clients-list {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    min-height: 120px;
    max-height: 200px;
    padding: 15px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.5);
}

.client-item {
    background: rgba(248, 249, 250, 0.8);
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.client-item:hover {
    background: rgba(248, 249, 250, 1);
    transform: translateX(2px);
}

.client-item:last-child {
    margin-bottom: 0;
}

.client-id {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.client-info {
    color: var(--secondary-color);
    font-size: 0.8rem;
    margin-top: 4px;
}

.no-clients {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
}

/* 实时数据样式 */
.data-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--dark-color);
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.live-data {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    color: #fff;
    border-radius: var(--border-radius);
    padding: 15px;
    flex: 1;
    overflow-y: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.live-data::-webkit-scrollbar {
    width: 8px;
}

.live-data::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.live-data::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.live-data::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.log-entry {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 4px;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.log-entry.system {
    color: #74b9ff;
    border-left-color: #74b9ff;
}

.log-entry.client {
    color: #00b894;
    border-left-color: #00b894;
}

.log-entry.live {
    color: #fdcb6e;
    border-left-color: #fdcb6e;
}

.log-entry.error {
    color: #fd79a8;
    border-left-color: #fd79a8;
    background: rgba(253, 121, 168, 0.1);
}

.timestamp {
    color: #888;
    margin-right: 10px;
    font-weight: 500;
}

.message {
    word-wrap: break-word;
    word-break: break-all;
}

/* 系统信息样式 */
.system-info {
    flex: 1;
    overflow-y: auto;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.info-card h4 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.9rem;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item span:first-child {
    color: var(--secondary-color);
    font-weight: 500;
}

.info-item span:last-child {
    color: var(--dark-color);
    font-weight: 600;
}

/* 通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1002;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow-medium);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
    position: relative;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.toast-title {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--secondary-color);
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-message {
    color: var(--secondary-color);
    font-size: 0.85rem;
    line-height: 1.4;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1003;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 10% auto;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow-heavy);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.5);
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--secondary-color);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--dark-color);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.5);
}

/* 连接历史样式 */
.history-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.history-count {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.connection-history {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.5);
}

.history-item {
    background: rgba(248, 249, 250, 0.8);
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid var(--info-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.history-item:hover {
    background: rgba(248, 249, 250, 1);
    transform: translateX(2px);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-url {
    font-weight: 600;
    color: var(--dark-color);
    word-break: break-all;
    margin-bottom: 4px;
}

.history-info {
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.no-history {
    text-align: center;
    color: var(--secondary-color);
    font-style: italic;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
}

/* 连接状态指示器 */
.connection-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.connection-status-active {
    background-color: var(--success-color);
}

.connection-status-inactive {
    background-color: var(--danger-color);
}

.connection-status-unknown {
    background-color: var(--secondary-color);
}

/* 数据持久化指示器 */
.persistence-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    color: white;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    font-size: 12px;
    opacity: 0;
    transition: var(--transition);
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 8px;
}

.persistence-indicator.show {
    opacity: 1;
    transform: translateY(-5px);
}

.indicator-icon {
    font-size: 14px;
}

/* 右键菜单 */
.context-menu {
    position: fixed;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 5px 0;
    z-index: 1004;
    display: none;
    min-width: 150px;
}

.context-menu-item {
    padding: 10px 15px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--dark-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-menu-item:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 320px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: none;
        overflow-y: visible;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .quick-buttons {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 5px;
        height: 100vh;
    }

    .top-nav {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px;
    }

    .nav-brand {
        flex-direction: column;
        gap: 5px;
    }

    .nav-brand h1 {
        font-size: 1.3rem;
    }

    .nav-status {
        flex-direction: column;
        gap: 10px;
    }

    .form-actions {
        flex-direction: column;
    }

    .data-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .history-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tab-nav {
        flex-direction: column;
    }

    .tab-btn {
        border-radius: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .tab-btn:last-child {
        border-bottom: none;
    }

    .toast-container {
        left: 10px;
        right: 10px;
        top: 10px;
    }

    .toast {
        min-width: auto;
        width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .persistence-indicator {
        bottom: 10px;
        right: 10px;
        left: 10px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .panel {
        padding: 15px;
    }

    .top-nav {
        padding: 10px;
    }

    .nav-brand h1 {
        font-size: 1.1rem;
    }

    .btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .form-control {
        padding: 8px 10px;
        font-size: 13px;
    }

    .live-data {
        font-size: 12px;
        padding: 10px;
    }

    .log-entry {
        padding: 4px 6px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-color: #ecf0f1;
        --light-color: #2c3e50;
        --border-color: #34495e;
    }

    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .panel {
        background: rgba(52, 73, 94, 0.95);
        color: var(--dark-color);
    }

    .top-nav {
        background: rgba(52, 73, 94, 0.95);
    }

    .form-control {
        background: rgba(44, 62, 80, 0.9);
        color: var(--dark-color);
        border-color: var(--border-color);
    }

    .clients-list,
    .connection-history {
        background: rgba(44, 62, 80, 0.5);
    }

    .client-item,
    .history-item {
        background: rgba(52, 73, 94, 0.8);
        color: var(--dark-color);
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .top-nav,
    .toast-container,
    .persistence-indicator,
    .modal,
    .context-menu {
        display: none !important;
    }

    .main-content {
        flex-direction: column;
    }

    .content-area {
        width: 100%;
    }

    .live-data {
        background: white !important;
        color: black !important;
        border: 1px solid #ccc;
    }

    .log-entry {
        border-left: none !important;
        background: none !important;
    }
}
