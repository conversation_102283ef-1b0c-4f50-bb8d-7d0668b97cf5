<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok直播监控工具 - 控制面板</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎯</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <nav class="top-nav">
            <div class="nav-brand">
                <h1>🎯 TikTok直播监控工具</h1>
                <span class="version">v2.0</span>
            </div>
            <div class="nav-status">
                <div class="connection-indicator">
                    <div id="connection-dot" class="status-dot status-disconnected"></div>
                    <span id="connection-status" class="status-text">初始化中...</span>
                </div>
                <button id="connect-btn" class="btn btn-primary btn-sm">连接服务器</button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧控制面板 -->
            <aside class="sidebar">
                <!-- 快速操作面板 -->
                <section class="panel quick-actions">
                    <h3>⚡ 快速操作</h3>
                    <div class="quick-buttons">
                        <button id="refresh-all" class="btn btn-info btn-block">🔄 刷新全部</button>
                        <button id="emergency-stop" class="btn btn-danger btn-block">🛑 紧急停止</button>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">客户端</span>
                            <span id="clients-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">活跃连接</span>
                            <span id="active-connections-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">运行时间</span>
                            <span id="uptime" class="stat-value">00:00</span>
                        </div>
                    </div>
                </section>

                <!-- 客户端管理 -->
                <section class="panel">
                    <h3>📱 客户端管理</h3>
                    <div class="clients-container">
                        <div class="clients-header">
                            <button id="refresh-clients" class="btn btn-secondary btn-sm">刷新列表</button>
                        </div>
                        <div id="clients-list" class="clients-list">
                            <div class="no-clients">暂无连接的客户端</div>
                        </div>
                    </div>
                </section>

                <!-- 直播间控制 -->
                <section class="panel">
                    <h3>🎬 直播间控制</h3>
                    <div class="control-form">
                        <div class="form-group">
                            <label for="client-select">选择客户端:</label>
                            <select id="client-select" class="form-control">
                                <option value="">请选择客户端</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="live-url">直播间URL:</label>
                            <div class="input-group">
                                <input type="text" id="live-url" class="form-control"
                                       placeholder="https://www.tiktok.com/@username/live">
                                <button id="validate-url" class="btn btn-outline" title="验证URL">✓</button>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="connect-room" class="btn btn-success">🔗 连接直播间</button>
                            <button id="disconnect-room" class="btn btn-danger">❌ 断开连接</button>
                            <button id="get-status" class="btn btn-info">📊 获取状态</button>
                        </div>
                    </div>
                </section>

                <!-- 窗口控制 -->
                <section class="panel">
                    <h3>🪟 窗口控制</h3>
                    <div class="window-control-form">
                        <div class="form-group">
                            <label for="connection-select">选择连接:</label>
                            <select id="connection-select" class="form-control">
                                <option value="">请选择活跃连接</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button id="show-window" class="btn btn-success">👁️ 显示窗口</button>
                            <button id="hide-window" class="btn btn-warning">🙈 隐藏窗口</button>
                            <button id="refresh-connections" class="btn btn-secondary">🔄 刷新连接</button>
                        </div>
                    </div>
                </section>

                <!-- 广播消息 -->
                <section class="panel">
                    <h3>📢 广播消息</h3>
                    <div class="broadcast-form">
                        <div class="form-group">
                            <label for="broadcast-message">广播消息:</label>
                            <div class="input-group">
                                <input type="text" id="broadcast-message" class="form-control"
                                       placeholder="输入要广播的消息" maxlength="200">
                                <span class="input-counter">0/200</span>
                            </div>
                        </div>
                        <button id="send-broadcast" class="btn btn-warning btn-block">📤 发送广播</button>
                    </div>
                </section>
            </aside>

            <!-- 右侧内容区域 -->
            <div class="content-area">
                <!-- 标签页导航 -->
                <div class="tab-nav">
                    <button class="tab-btn active" data-tab="live-data">📊 实时数据</button>
                    <button class="tab-btn" data-tab="connection-history">📋 连接历史</button>
                    <button class="tab-btn" data-tab="system-info">⚙️ 系统信息</button>
                </div>

                <!-- 实时数据标签页 -->
                <div id="live-data-tab" class="tab-content active">
                    <div class="panel full-height">
                        <div class="panel-header">
                            <h3>📊 实时数据监控</h3>
                            <div class="data-controls">
                                <button id="clear-logs" class="btn btn-secondary btn-sm">🗑️ 清空日志</button>
                                <button id="export-logs" class="btn btn-info btn-sm">💾 导出日志</button>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="auto-scroll" checked>
                                    <span class="checkmark"></span>
                                    自动滚动
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="filter-errors" checked>
                                    <span class="checkmark"></span>
                                    显示错误
                                </label>
                            </div>
                        </div>
                        <div id="live-data" class="live-data">
                            <div class="log-entry system">
                                <span class="timestamp">[系统]</span>
                                <span class="message">等待连接到服务器...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 连接历史标签页 -->
                <div id="connection-history-tab" class="tab-content">
                    <div class="panel full-height">
                        <div class="panel-header">
                            <h3>📋 连接历史记录</h3>
                            <div class="history-controls">
                                <button id="clear-history" class="btn btn-secondary btn-sm">🗑️ 清空历史</button>
                                <button id="export-history" class="btn btn-info btn-sm">💾 导出历史</button>
                                <span id="history-count" class="history-count">历史记录: 0</span>
                            </div>
                        </div>
                        <div id="connection-history" class="connection-history">
                            <div class="no-history">暂无连接历史</div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息标签页 -->
                <div id="system-info-tab" class="tab-content">
                    <div class="panel full-height">
                        <div class="panel-header">
                            <h3>⚙️ 系统信息</h3>
                        </div>
                        <div class="system-info">
                            <div class="info-grid">
                                <div class="info-card">
                                    <h4>🌐 网络状态</h4>
                                    <div class="info-item">
                                        <span>WebSocket状态:</span>
                                        <span id="ws-status">未连接</span>
                                    </div>
                                    <div class="info-item">
                                        <span>服务器地址:</span>
                                        <span id="server-address">ws://localhost:8087</span>
                                    </div>
                                    <div class="info-item">
                                        <span>重连次数:</span>
                                        <span id="reconnect-count">0</span>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>📈 性能统计</h4>
                                    <div class="info-item">
                                        <span>消息总数:</span>
                                        <span id="message-count">0</span>
                                    </div>
                                    <div class="info-item">
                                        <span>错误次数:</span>
                                        <span id="error-count">0</span>
                                    </div>
                                    <div class="info-item">
                                        <span>内存使用:</span>
                                        <span id="memory-usage">--</span>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>🔧 应用信息</h4>
                                    <div class="info-item">
                                        <span>版本:</span>
                                        <span>v2.0.0</span>
                                    </div>
                                    <div class="info-item">
                                        <span>构建时间:</span>
                                        <span id="build-time">--</span>
                                    </div>
                                    <div class="info-item">
                                        <span>浏览器:</span>
                                        <span id="browser-info">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 浮动通知 -->
    <div id="toast-container" class="toast-container"></div>

    <!-- 数据持久化指示器 -->
    <div id="persistence-indicator" class="persistence-indicator">
        <span class="indicator-icon">💾</span>
        <span class="indicator-text">数据已保存</span>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在连接服务器...</p>
        </div>
    </div>

    <!-- 增强的模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">提示</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p id="modal-message"></p>
            </div>
            <div class="modal-footer">
                <button id="modal-cancel" class="btn btn-secondary" style="display: none;">取消</button>
                <button id="modal-ok" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="context-menu" class="context-menu">
        <div class="context-menu-item" data-action="copy">📋 复制</div>
        <div class="context-menu-item" data-action="clear">🗑️ 清除</div>
        <div class="context-menu-item" data-action="export">💾 导出</div>
    </div>

    <script src="protobuf.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
