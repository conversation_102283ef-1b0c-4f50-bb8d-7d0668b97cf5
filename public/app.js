class TikTokMonitorApp {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.clients = [];
        this.protoHelper = null;
        this.connectionHistory = [];
        this.activeConnections = new Map(); // connectionId -> {clientId, liveUrl, status}
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000; // 3秒
        this.heartbeatInterval = null;
        this.heartbeatTimeout = null;
        this.connectionRefreshInterval = null;
        this.startTime = Date.now();
        this.messageCount = 0;
        this.errorCount = 0;
        this.currentTab = 'live-data';

        this.initializeElements();
        this.bindEvents();
        this.initializeProtobuf();
        this.loadPersistedData();
        this.initializeSystemInfo();
        this.startUptimeCounter();

        // 自动连接到服务器
        this.autoConnect();

        // 监听网络状态变化
        this.setupNetworkListeners();
    }

    initializeElements() {
        // 连接状态相关
        this.connectionStatus = document.getElementById('connection-status');
        this.connectionDot = document.getElementById('connection-dot');
        this.connectBtn = document.getElementById('connect-btn');

        // 快速操作相关
        this.refreshAllBtn = document.getElementById('refresh-all');
        this.emergencyStopBtn = document.getElementById('emergency-stop');
        this.clientsCount = document.getElementById('clients-count');
        this.activeConnectionsCount = document.getElementById('active-connections-count');
        this.uptimeElement = document.getElementById('uptime');

        // 客户端管理相关
        this.refreshClientsBtn = document.getElementById('refresh-clients');
        this.clientsList = document.getElementById('clients-list');
        this.clientSelect = document.getElementById('client-select');

        // 直播间控制相关
        this.liveUrlInput = document.getElementById('live-url');
        this.validateUrlBtn = document.getElementById('validate-url');
        this.connectRoomBtn = document.getElementById('connect-room');
        this.disconnectRoomBtn = document.getElementById('disconnect-room');
        this.getStatusBtn = document.getElementById('get-status');

        // 广播消息相关
        this.broadcastMessageInput = document.getElementById('broadcast-message');
        this.sendBroadcastBtn = document.getElementById('send-broadcast');

        // 标签页相关
        this.tabButtons = document.querySelectorAll('.tab-btn');
        this.tabContents = document.querySelectorAll('.tab-content');

        // 实时数据相关
        this.liveData = document.getElementById('live-data');
        this.clearLogsBtn = document.getElementById('clear-logs');
        this.exportLogsBtn = document.getElementById('export-logs');
        this.autoScrollCheckbox = document.getElementById('auto-scroll');
        this.filterErrorsCheckbox = document.getElementById('filter-errors');

        // 窗口控制相关
        this.connectionSelect = document.getElementById('connection-select');
        this.showWindowBtn = document.getElementById('show-window');
        this.hideWindowBtn = document.getElementById('hide-window');
        this.refreshConnectionsBtn = document.getElementById('refresh-connections');

        // 连接历史相关
        this.connectionHistoryElement = document.getElementById('connection-history');
        this.historyCount = document.getElementById('history-count');
        this.clearHistoryBtn = document.getElementById('clear-history');
        this.exportHistoryBtn = document.getElementById('export-history');

        // 系统信息相关
        this.wsStatusElement = document.getElementById('ws-status');
        this.serverAddressElement = document.getElementById('server-address');
        this.reconnectCountElement = document.getElementById('reconnect-count');
        this.messageCountElement = document.getElementById('message-count');
        this.errorCountElement = document.getElementById('error-count');
        this.memoryUsageElement = document.getElementById('memory-usage');
        this.buildTimeElement = document.getElementById('build-time');
        this.browserInfoElement = document.getElementById('browser-info');

        // 持久化指示器
        this.persistenceIndicator = document.getElementById('persistence-indicator');
        this.toastContainer = document.getElementById('toast-container');
        this.loadingOverlay = document.getElementById('loading-overlay');

        // 模态框相关
        this.modal = document.getElementById('modal');
        this.modalTitle = document.getElementById('modal-title');
        this.modalMessage = document.getElementById('modal-message');
        this.modalOk = document.getElementById('modal-ok');
        this.modalCancel = document.getElementById('modal-cancel');
        this.modalClose = document.querySelector('.close');

        // 右键菜单
        this.contextMenu = document.getElementById('context-menu');
    }

    bindEvents() {
        // 连接按钮
        this.connectBtn.addEventListener('click', () => {
            if (this.isConnected) {
                this.disconnect();
            } else {
                this.reconnectAttempts = 0; // 重置重连计数
                this.connect();
            }
        });

        // 快速操作
        this.refreshAllBtn.addEventListener('click', () => {
            this.refreshAll();
        });

        this.emergencyStopBtn.addEventListener('click', () => {
            this.emergencyStop();
        });

        // 刷新客户端列表
        this.refreshClientsBtn.addEventListener('click', () => {
            this.refreshClients();
        });

        // 直播间控制
        this.validateUrlBtn.addEventListener('click', () => {
            this.validateUrl();
        });

        this.connectRoomBtn.addEventListener('click', () => {
            this.connectToRoom();
        });

        this.disconnectRoomBtn.addEventListener('click', () => {
            this.disconnectFromRoom();
        });

        this.getStatusBtn.addEventListener('click', () => {
            this.getClientStatus();
        });

        // 广播消息
        this.broadcastMessageInput.addEventListener('input', () => {
            this.updateInputCounter();
        });

        this.broadcastMessageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendBroadcast();
            }
        });

        this.sendBroadcastBtn.addEventListener('click', () => {
            this.sendBroadcast();
        });

        // 标签页切换
        this.tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab(btn.dataset.tab);
            });
        });

        // 实时数据控制
        this.clearLogsBtn.addEventListener('click', () => {
            this.clearLogs();
        });

        this.exportLogsBtn.addEventListener('click', () => {
            this.exportLogs();
        });

        // 窗口控制
        this.showWindowBtn.addEventListener('click', () => {
            this.showWindow();
        });

        this.hideWindowBtn.addEventListener('click', () => {
            this.hideWindow();
        });

        this.refreshConnectionsBtn.addEventListener('click', () => {
            this.refreshActiveConnections();
        });

        // 连接历史
        this.clearHistoryBtn.addEventListener('click', () => {
            this.clearConnectionHistory();
        });

        this.exportHistoryBtn.addEventListener('click', () => {
            this.exportHistory();
        });

        // 添加测试连接（用于演示窗口控制功能）
        this.addTestConnectionBtn = document.createElement('button');
        this.addTestConnectionBtn.textContent = '添加测试连接';
        this.addTestConnectionBtn.className = 'btn btn-info btn-sm';
        this.addTestConnectionBtn.style.marginLeft = '10px';
        this.addTestConnectionBtn.addEventListener('click', () => {
            this.addTestConnection();
        });
        this.refreshConnectionsBtn.parentNode.appendChild(this.addTestConnectionBtn);

        // 模态框
        this.modalOk.addEventListener('click', () => {
            this.hideModal();
        });

        this.modalCancel.addEventListener('click', () => {
            this.hideModal();
        });

        this.modalClose.addEventListener('click', () => {
            this.hideModal();
        });

        // 右键菜单
        this.liveData.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e.clientX, e.clientY);
        });

        document.addEventListener('click', () => {
            this.hideContextMenu();
        });

        this.contextMenu.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleContextMenuAction(action);
            }
        });

        // 回车键发送
        this.broadcastMessageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendBroadcast();
            }
        });

        this.liveUrlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.connectToRoom();
            }
        });
    }

    initializeProtobuf() {
        // 这里应该初始化protobuf，但由于复杂性，我们先使用JSON格式
        this.addLog('system', '初始化protobuf支持...');
    }

    // 加载持久化数据
    loadPersistedData() {
        try {
            // 加载连接历史
            const savedHistory = localStorage.getItem('tiktok-monitor-history');
            if (savedHistory) {
                this.connectionHistory = JSON.parse(savedHistory);
                this.addLog('system', `已加载 ${this.connectionHistory.length} 条历史记录`);
            }

            // 加载活跃连接
            const savedConnections = localStorage.getItem('tiktok-monitor-connections');
            if (savedConnections) {
                const connections = JSON.parse(savedConnections);
                connections.forEach(conn => {
                    this.activeConnections.set(conn.connectionId, conn);
                });
                this.addLog('system', `已加载 ${this.activeConnections.size} 个活跃连接`);
            }

            // 加载日志（最近100条）
            const savedLogs = localStorage.getItem('tiktok-monitor-logs');
            if (savedLogs) {
                const logs = JSON.parse(savedLogs);
                logs.forEach(log => {
                    this.restoreLogEntry(log.type, log.message, log.timestamp);
                });
            }

            // 更新界面显示
            this.updateConnectionHistoryDisplay();
            this.updateConnectionSelect();
        } catch (error) {
            this.addLog('error', `加载持久化数据失败: ${error.message}`);
        }
    }

    // 保存数据到localStorage
    savePersistedData() {
        try {
            // 保存连接历史（最近50条）
            const historyToSave = this.connectionHistory.slice(-50);
            localStorage.setItem('tiktok-monitor-history', JSON.stringify(historyToSave));

            // 保存活跃连接
            const connectionsToSave = Array.from(this.activeConnections.values());
            localStorage.setItem('tiktok-monitor-connections', JSON.stringify(connectionsToSave));

            // 保存日志（最近100条）
            const logEntries = Array.from(this.liveData.querySelectorAll('.log-entry')).slice(-100);
            const logsToSave = logEntries.map(entry => ({
                type: entry.className.replace('log-entry ', ''),
                message: entry.querySelector('.message').textContent,
                timestamp: entry.querySelector('.timestamp').textContent
            }));
            localStorage.setItem('tiktok-monitor-logs', JSON.stringify(logsToSave));
        } catch (error) {
            console.error('保存持久化数据失败:', error);
        }
    }

    // 恢复日志条目
    restoreLogEntry(type, message, timestamp) {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;

        logEntry.innerHTML = `
            <span class="timestamp">${timestamp}</span>
            <span class="message">${message}</span>
        `;

        this.liveData.appendChild(logEntry);
    }

    // 设置网络状态监听
    setupNetworkListeners() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.addLog('system', '🌐 网络已恢复，尝试重新连接...');
            if (!this.isConnected) {
                this.reconnectAttempts = 0; // 重置重连计数
                setTimeout(() => this.connect(), 1000);
            }
        });

        window.addEventListener('offline', () => {
            this.addLog('system', '🌐 网络连接已断开');
        });

        // 页面可见性变化时检查连接
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.addLog('system', '页面重新激活，检查连接状态...');
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.reconnectAttempts = 0;
                        this.connect();
                    }
                }, 2000);
            }
        });
    }

    // 自动连接到服务器
    autoConnect() {
        this.addLog('system', '🚀 正在自动连接到服务器...');

        // 延迟一秒后开始连接，确保界面完全加载
        setTimeout(() => {
            this.connect();
        }, 1000);
    }

    connect() {
        if (this.isConnected) return;

        this.updateConnectionStatus('connecting');
        this.addLog('system', '正在连接到WebSocket服务器...');

        try {
            this.ws = new WebSocket('ws://localhost:8087');
            
            this.ws.onopen = () => {
                this.isConnected = true;
                this.reconnectAttempts = 0; // 重置重连计数
                this.updateConnectionStatus('connected');
                this.addLog('system', '✅ 已连接到WebSocket服务器');
                this.refreshClients();
                this.startHeartbeat();
                this.startConnectionRefresh();
            };

            this.ws.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.ws.onclose = (event) => {
                this.isConnected = false;
                this.stopHeartbeat();
                this.stopConnectionRefresh();
                this.updateConnectionStatus('disconnected');

                if (event.wasClean) {
                    this.addLog('system', '❌ WebSocket连接已正常关闭');
                } else {
                    this.addLog('system', '❌ WebSocket连接意外断开');
                    this.attemptReconnect();
                }
            };

            this.ws.onerror = (error) => {
                this.addLog('error', `WebSocket错误: ${error.message || '连接失败'}`);
                this.updateConnectionStatus('disconnected');
            };

        } catch (error) {
            this.addLog('error', `连接失败: ${error.message}`);
            this.updateConnectionStatus('disconnected');
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnected = false;
        this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自动重连
        this.stopHeartbeat();
        this.stopConnectionRefresh();
        this.updateConnectionStatus('disconnected');
        this.addLog('system', '已手动断开WebSocket连接');
    }

    // 开始心跳检测
    startHeartbeat() {
        this.stopHeartbeat(); // 清除之前的心跳

        // 每30秒发送一次心跳
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
                try {
                    // 发送ping消息
                    this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));

                    // 设置心跳超时检测
                    this.heartbeatTimeout = setTimeout(() => {
                        this.addLog('error', '❤️ 心跳超时，连接可能已断开');
                        if (this.ws) {
                            this.ws.close();
                        }
                    }, 10000); // 10秒超时
                } catch (error) {
                    this.addLog('error', `心跳发送失败: ${error.message}`);
                }
            }
        }, 30000);
    }

    // 停止心跳检测
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    // 开始连接状态刷新
    startConnectionRefresh() {
        this.stopConnectionRefresh(); // 清除之前的定时器

        // 每15秒刷新一次连接状态
        this.connectionRefreshInterval = setInterval(() => {
            if (this.isConnected) {
                this.refreshActiveConnections();
            }
        }, 15000);
    }

    // 停止连接状态刷新
    stopConnectionRefresh() {
        if (this.connectionRefreshInterval) {
            clearInterval(this.connectionRefreshInterval);
            this.connectionRefreshInterval = null;
        }
    }

    // 尝试重连
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.addLog('error', `❌ 重连失败，已达到最大重连次数 (${this.maxReconnectAttempts})`);
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts; // 递增延迟

        this.addLog('system', `🔄 第 ${this.reconnectAttempts} 次重连尝试，${delay/1000} 秒后开始...`);
        this.updateConnectionStatus('connecting');

        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    updateConnectionStatus(status) {
        this.connectionStatus.className = `status-${status}`;

        switch (status) {
            case 'connected':
                this.connectionStatus.textContent = '已连接';
                this.connectBtn.textContent = '断开连接';
                this.connectBtn.className = 'btn btn-danger';
                this.connectBtn.disabled = false;
                break;
            case 'connecting':
                if (this.reconnectAttempts > 0) {
                    this.connectionStatus.textContent = `重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`;
                    this.connectBtn.textContent = '重连中...';
                } else {
                    this.connectionStatus.textContent = '连接中...';
                    this.connectBtn.textContent = '连接中...';
                }
                this.connectBtn.disabled = true;
                break;
            case 'disconnected':
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    this.connectionStatus.textContent = '连接失败';
                    this.connectBtn.textContent = '重新连接';
                } else {
                    this.connectionStatus.textContent = '未连接';
                    this.connectBtn.textContent = '连接服务器';
                }
                this.connectBtn.className = 'btn btn-primary';
                this.connectBtn.disabled = false;
                break;
        }
    }

    handleMessage(data) {
        try {
            // 尝试解析为JSON（简化版本）
            if (typeof data === 'string') {
                const message = JSON.parse(data);
                this.processMessage(message);
            } else {
                // 这里应该处理protobuf二进制数据
                this.addLog('system', '收到protobuf消息（暂未完全支持）');
            }
        } catch (error) {
            this.addLog('error', `消息解析错误: ${error.message}`);
        }
    }

    processMessage(message) {
        switch (message.type) {
            case 'welcome':
                this.addLog('system', `服务器欢迎消息: ${message.message}`);
                break;
            case 'live_events':
                this.handleLiveEvents(message);
                break;
            case 'server_broadcast':
                this.addLog('system', `📢 服务器广播: ${message.message}`);
                break;
            case 'error':
                this.addLog('error', `服务器错误: ${message.message}`);
                break;
            case 'pong':
                // 收到心跳响应，清除超时
                if (this.heartbeatTimeout) {
                    clearTimeout(this.heartbeatTimeout);
                    this.heartbeatTimeout = null;
                }
                break;
            default:
                this.addLog('system', `收到消息: ${JSON.stringify(message)}`);
        }
    }

    handleLiveEvents(message) {
        const data = message.data;
        if (data && data.events_data) {
            data.events_data.forEach(event => {
                this.processLiveEvent(event, message.liveUrl);
            });
        }
    }

    processLiveEvent(event, liveUrl) {
        const timestamp = new Date().toLocaleTimeString();
        
        switch (event.msg_type) {
            case 1: // 点赞
                if (event.like_msg) {
                    this.addLog('live', `👍 ${this.formatUser(event.like_msg.user)} 点赞x${event.like_msg.like_count}`);
                }
                break;
            case 2: // 评论
                if (event.comment_msg) {
                    this.addLog('live', `💬 ${this.formatUser(event.comment_msg.user)}: ${event.comment_msg.content}`);
                }
                break;
            case 3: // 礼物
                if (event.gift_msg) {
                    const gift = event.gift_msg;
                    this.addLog('live', `🎁 ${this.formatUser(gift.user)} 送出 ${gift.gift_name} x${gift.count}`);
                }
                break;
            case 4: // 进入
                if (event.member_msg) {
                    this.addLog('live', `👋 ${this.formatUser(event.member_msg.user)} 进入直播间`);
                }
                break;
            case 5: // 直播控制消息
                if (event.control_msg) {
                    this.handleControlMessage(event.control_msg);
                }
                break;
        }
    }

    handleControlMessage(control) {
        switch (control.action) {
            case 0:
                this.addLog('live', `🔴 ${control.room_name} 正在直播`);
                break;
            case 1:
                this.addLog('live', `⏹️ 直播结束`);
                break;
            case 100:
                this.addLog('error', `❌ URL错误: ${control.live_url}`);
                break;
            case 101:
                this.addLog('error', `❌ 其他错误: ${control.action_msg}`);
                break;
        }
    }

    formatUser(user) {
        if (!user) return '未知用户';
        let name = user.nick_name || user.user_name || '未知用户';
        if (user.level && user.level > 0) {
            name = `Lv.${user.level} ${name}`;
        }
        return name;
    }

    async refreshClients() {
        try {
            const response = await fetch('/api/clients');
            const clients = await response.json();
            this.clients = clients;
            this.updateClientsList();
            this.updateClientsCount();
            this.updateClientSelect();

            // 同时刷新活跃连接
            await this.refreshActiveConnections();
        } catch (error) {
            this.addLog('error', `获取客户端列表失败: ${error.message}`);
        }
    }

    updateClientsList() {
        if (this.clients.length === 0) {
            this.clientsList.innerHTML = '<div class="no-clients">暂无连接的客户端</div>';
            return;
        }

        const html = this.clients.map(client => `
            <div class="client-item">
                <div class="client-id">客户端ID: ${client.id}</div>
                <div class="client-info">
                    上下文: ${client.context || '未知'} | 
                    连接数: ${client.totalConnections} | 
                    连接时间: ${new Date(client.connectedAt).toLocaleString()}
                </div>
            </div>
        `).join('');

        this.clientsList.innerHTML = html;
    }

    updateClientsCount() {
        this.clientsCount.textContent = `客户端数量: ${this.clients.length}`;
    }

    updateClientSelect() {
        const options = ['<option value="">请选择客户端</option>'];
        this.clients.forEach(client => {
            options.push(`<option value="${client.id}">${client.id} (${client.context || '未知'})</option>`);
        });
        this.clientSelect.innerHTML = options.join('');
    }

    async connectToRoom() {
        const clientId = this.clientSelect.value;
        const liveUrl = this.liveUrlInput.value.trim();

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        if (!liveUrl) {
            this.showModal('错误', '请输入直播间URL');
            return;
        }

        try {
            const response = await fetch('/api/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId, liveUrl })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📡 已请求客户端 ${clientId} 连接到 ${liveUrl}`);

                // 添加到活跃连接（使用requestId作为临时connectionId）
                const tempConnectionId = result.requestId;
                this.activeConnections.set(tempConnectionId, {
                    clientId: clientId,
                    liveUrl: liveUrl,
                    status: 'connecting',
                    windowVisible: false,
                    timestamp: Date.now()
                });

                // 添加到连接历史
                this.addToConnectionHistory(clientId, liveUrl, tempConnectionId);

                // 更新连接选择器
                this.updateConnectionSelect();

                this.showModal('成功', '连接请求已发送');
            } else {
                this.addLog('error', `连接请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `连接请求失败: ${error.message}`);
            this.showModal('错误', `连接请求失败: ${error.message}`);
        }
    }

    async disconnectFromRoom() {
        const clientId = this.clientSelect.value;

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        try {
            const response = await fetch('/api/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📡 已请求客户端 ${clientId} 断开连接`);
                this.showModal('成功', '断开请求已发送');
            } else {
                this.addLog('error', `断开请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `断开请求失败: ${error.message}`);
            this.showModal('错误', `断开请求失败: ${error.message}`);
        }
    }

    async getClientStatus() {
        const clientId = this.clientSelect.value;

        if (!clientId) {
            this.showModal('错误', '请选择一个客户端');
            return;
        }

        try {
            const response = await fetch('/api/status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ clientId })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `📊 已请求客户端 ${clientId} 状态信息`);
                this.showModal('成功', '状态请求已发送');
            } else {
                this.addLog('error', `状态请求失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `状态请求失败: ${error.message}`);
            this.showModal('错误', `状态请求失败: ${error.message}`);
        }
    }

    async sendBroadcast() {
        const message = this.broadcastMessageInput.value.trim();

        if (!message) {
            this.showModal('错误', '请输入广播消息');
            return;
        }

        try {
            const response = await fetch('/api/broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('system', `📢 广播消息已发送: ${message}`);
                this.broadcastMessageInput.value = '';
                this.showModal('成功', '广播消息已发送');
            } else {
                this.addLog('error', `广播失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `广播失败: ${error.message}`);
            this.showModal('错误', `广播失败: ${error.message}`);
        }
    }

    addLog(type, message) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;

        logEntry.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="message">${message}</span>
        `;

        this.liveData.appendChild(logEntry);

        // 自动滚动
        if (this.autoScrollCheckbox.checked) {
            this.liveData.scrollTop = this.liveData.scrollHeight;
        }

        // 限制日志条数
        const logs = this.liveData.querySelectorAll('.log-entry');
        if (logs.length > 1000) {
            logs[0].remove();
        }

        // 定期保存数据（每10条日志保存一次）
        if (logs.length % 10 === 0) {
            this.savePersistedData();
        }
    }

    clearLogs() {
        this.liveData.innerHTML = '';
        this.addLog('system', '日志已清空');
    }

    showModal(title, message) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.modal.style.display = 'block';
    }

    hideModal() {
        this.modal.style.display = 'none';
    }

    // 显示窗口
    async showWindow() {
        const connectionId = this.connectionSelect.value;
        if (!connectionId) {
            this.showModal('错误', '请选择一个连接');
            return;
        }

        const connection = this.activeConnections.get(connectionId);
        if (!connection) {
            this.showModal('错误', '连接不存在');
            return;
        }

        try {
            const response = await fetch('/api/show-window', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clientId: connection.clientId,
                    connectionId: connectionId,
                    show: true
                })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `🪟 已请求显示窗口: ${connectionId}`);
                this.showModal('成功', '显示窗口请求已发送');
            } else {
                this.addLog('error', `显示窗口失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `显示窗口失败: ${error.message}`);
            this.showModal('错误', `显示窗口失败: ${error.message}`);
        }
    }

    // 隐藏窗口
    async hideWindow() {
        const connectionId = this.connectionSelect.value;
        if (!connectionId) {
            this.showModal('错误', '请选择一个连接');
            return;
        }

        const connection = this.activeConnections.get(connectionId);
        if (!connection) {
            this.showModal('错误', '连接不存在');
            return;
        }

        try {
            const response = await fetch('/api/show-window', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    clientId: connection.clientId,
                    connectionId: connectionId,
                    show: false
                })
            });

            const result = await response.json();
            if (result.success) {
                this.addLog('client', `🪟 已请求隐藏窗口: ${connectionId}`);
                this.showModal('成功', '隐藏窗口请求已发送');
            } else {
                this.addLog('error', `隐藏窗口失败: ${result.error}`);
                this.showModal('错误', result.error);
            }
        } catch (error) {
            this.addLog('error', `隐藏窗口失败: ${error.message}`);
            this.showModal('错误', `隐藏窗口失败: ${error.message}`);
        }
    }

    // 刷新活跃连接
    async refreshActiveConnections() {
        try {
            const response = await fetch('/api/connections');
            const connections = await response.json();

            // 清空现有连接
            this.activeConnections.clear();

            // 添加新连接
            connections.forEach(conn => {
                this.activeConnections.set(conn.connectionId, {
                    clientId: conn.clientId,
                    liveUrl: conn.liveUrl,
                    status: conn.status,
                    windowVisible: conn.windowVisible,
                    timestamp: conn.timestamp
                });
            });

            this.updateConnectionSelect();
            this.addLog('system', `已刷新活跃连接列表，找到 ${connections.length} 个连接`);
        } catch (error) {
            this.addLog('error', `获取活跃连接失败: ${error.message}`);
        }
    }

    // 更新连接选择器
    updateConnectionSelect() {
        const options = ['<option value="">请选择活跃连接</option>'];
        this.activeConnections.forEach((connection, connectionId) => {
            const status = connection.status || 'unknown';
            const statusIcon = status === 'active' ? '🟢' : status === 'inactive' ? '🔴' : '⚪';
            options.push(`<option value="${connectionId}">${statusIcon} ${connectionId} (${connection.liveUrl})</option>`);
        });
        this.connectionSelect.innerHTML = options.join('');
    }

    // 添加连接到历史
    addToConnectionHistory(clientId, liveUrl, connectionId) {
        const historyItem = {
            clientId,
            liveUrl,
            connectionId,
            timestamp: Date.now(),
            status: 'connected'
        };

        this.connectionHistory.unshift(historyItem);

        // 限制历史记录数量
        if (this.connectionHistory.length > 100) {
            this.connectionHistory = this.connectionHistory.slice(0, 100);
        }

        this.updateConnectionHistoryDisplay();
        this.savePersistedData();
        this.showPersistenceIndicator();
    }

    // 更新连接历史显示
    updateConnectionHistoryDisplay() {
        if (this.connectionHistory.length === 0) {
            this.connectionHistoryElement.innerHTML = '<div class="no-history">暂无连接历史</div>';
            this.historyCount.textContent = '历史记录: 0';
            return;
        }

        const html = this.connectionHistory.slice(0, 20).map(item => `
            <div class="history-item">
                <div class="history-url">${item.liveUrl}</div>
                <div class="history-info">
                    客户端: ${item.clientId} |
                    连接ID: ${item.connectionId} |
                    时间: ${new Date(item.timestamp).toLocaleString()}
                </div>
            </div>
        `).join('');

        this.connectionHistoryElement.innerHTML = html;
        this.historyCount.textContent = `历史记录: ${this.connectionHistory.length}`;
    }

    // 清空连接历史
    clearConnectionHistory() {
        this.connectionHistory = [];
        this.updateConnectionHistoryDisplay();
        this.savePersistedData();
        this.addLog('system', '连接历史已清空');
    }

    // 显示持久化指示器
    showPersistenceIndicator() {
        this.persistenceIndicator.classList.add('show');
        setTimeout(() => {
            this.persistenceIndicator.classList.remove('show');
        }, 2000);
    }

    // 添加测试连接（用于演示窗口控制功能）
    addTestConnection() {
        const testConnections = [
            {
                connectionId: 'test-conn-001',
                clientId: 'test-client-001',
                liveUrl: 'https://www.tiktok.com/@test1/live',
                status: 'active',
                windowVisible: true,
                timestamp: Date.now()
            },
            {
                connectionId: 'test-conn-002',
                clientId: 'test-client-002',
                liveUrl: 'https://www.tiktok.com/@test2/live',
                status: 'active',
                windowVisible: false,
                timestamp: Date.now() - 60000
            },
            {
                connectionId: 'test-conn-003',
                clientId: 'test-client-003',
                liveUrl: 'https://www.tiktok.com/@test3/live',
                status: 'connecting',
                windowVisible: false,
                timestamp: Date.now() - 120000
            }
        ];

        testConnections.forEach(conn => {
            this.activeConnections.set(conn.connectionId, conn);
        });

        this.updateConnectionSelect();
        this.addLog('system', `已添加 ${testConnections.length} 个测试连接用于演示窗口控制功能`);
        this.showToast('成功', '测试连接已添加，现在可以在窗口控制区域看到连接列表了！', 'success');
    }

    // 新增方法：系统信息初始化
    initializeSystemInfo() {
        this.buildTimeElement.textContent = new Date().toLocaleString();
        this.browserInfoElement.textContent = this.getBrowserInfo();
        this.updateSystemInfo();
    }

    // 获取浏览器信息
    getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';

        if (ua.includes('Chrome')) browser = 'Chrome';
        else if (ua.includes('Firefox')) browser = 'Firefox';
        else if (ua.includes('Safari')) browser = 'Safari';
        else if (ua.includes('Edge')) browser = 'Edge';

        return `${browser} ${navigator.platform}`;
    }

    // 更新系统信息
    updateSystemInfo() {
        this.wsStatusElement.textContent = this.isConnected ? '已连接' : '未连接';
        this.reconnectCountElement.textContent = this.reconnectAttempts.toString();
        this.messageCountElement.textContent = this.messageCount.toString();
        this.errorCountElement.textContent = this.errorCount.toString();

        // 更新内存使用情况（如果支持）
        if (performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            this.memoryUsageElement.textContent = `${used}MB / ${total}MB`;
        } else {
            this.memoryUsageElement.textContent = '不支持';
        }
    }

    // 开始运行时间计数器
    startUptimeCounter() {
        setInterval(() => {
            const uptime = Date.now() - this.startTime;
            const hours = Math.floor(uptime / (1000 * 60 * 60));
            const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
            this.uptimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    // 标签页切换
    switchTab(tabName) {
        this.currentTab = tabName;

        // 更新标签按钮状态
        this.tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // 更新标签内容显示
        this.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });

        // 如果切换到系统信息标签，更新信息
        if (tabName === 'system-info') {
            this.updateSystemInfo();
        }
    }

    // 快速刷新所有
    refreshAll() {
        this.showToast('刷新', '正在刷新所有数据...', 'info');
        this.refreshClients();
        this.refreshActiveConnections();
        this.updateSystemInfo();
    }

    // 紧急停止
    emergencyStop() {
        this.showModal('确认', '确定要执行紧急停止吗？这将断开所有连接。', 'warning', () => {
            this.disconnect();
            this.activeConnections.clear();
            this.updateConnectionSelect();
            this.addLog('system', '执行紧急停止，所有连接已断开');
            this.showToast('紧急停止', '所有连接已断开', 'warning');
        });
    }

    // URL验证
    validateUrl() {
        const url = this.liveUrlInput.value.trim();
        if (!url) {
            this.showToast('验证失败', '请输入URL', 'error');
            return;
        }

        const isValid = /^https?:\/\/(www\.)?tiktok\.com\/@[\w.-]+\/live$/i.test(url);
        if (isValid) {
            this.showToast('验证成功', 'URL格式正确', 'success');
            this.liveUrlInput.style.borderColor = 'var(--success-color)';
        } else {
            this.showToast('验证失败', 'URL格式不正确', 'error');
            this.liveUrlInput.style.borderColor = 'var(--danger-color)';
        }

        setTimeout(() => {
            this.liveUrlInput.style.borderColor = '';
        }, 3000);
    }

    // 更新输入计数器
    updateInputCounter() {
        const counter = document.querySelector('.input-counter');
        if (counter) {
            const length = this.broadcastMessageInput.value.length;
            counter.textContent = `${length}/200`;
            counter.style.color = length > 180 ? 'var(--danger-color)' : 'var(--secondary-color)';
        }
    }

    // 导出日志
    exportLogs() {
        const logs = Array.from(this.liveData.children).map(entry => {
            const timestamp = entry.querySelector('.timestamp')?.textContent || '';
            const message = entry.querySelector('.message')?.textContent || '';
            return `${timestamp} ${message}`;
        }).join('\n');

        this.downloadFile('live-data-logs.txt', logs);
        this.showToast('导出成功', '日志已导出', 'success');
    }

    // 导出历史记录
    exportHistory() {
        const history = this.connectionHistory.map(item =>
            `${item.timestamp} - 客户端: ${item.clientId}, URL: ${item.liveUrl}`
        ).join('\n');

        this.downloadFile('connection-history.txt', history);
        this.showToast('导出成功', '历史记录已导出', 'success');
    }

    // 下载文件
    downloadFile(filename, content) {
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 显示通知
    showToast(title, message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <span class="toast-title">${title}</span>
                <button class="toast-close">&times;</button>
            </div>
            <div class="toast-message">${message}</div>
        `;

        this.toastContainer.appendChild(toast);

        // 自动关闭
        const autoClose = setTimeout(() => {
            this.removeToast(toast);
        }, duration);

        // 手动关闭
        toast.querySelector('.toast-close').addEventListener('click', () => {
            clearTimeout(autoClose);
            this.removeToast(toast);
        });
    }

    // 移除通知
    removeToast(toast) {
        toast.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    // 显示右键菜单
    showContextMenu(x, y) {
        this.contextMenu.style.display = 'block';
        this.contextMenu.style.left = `${x}px`;
        this.contextMenu.style.top = `${y}px`;
    }

    // 隐藏右键菜单
    hideContextMenu() {
        this.contextMenu.style.display = 'none';
    }

    // 处理右键菜单操作
    handleContextMenuAction(action) {
        switch (action) {
            case 'copy':
                this.copyLogsToClipboard();
                break;
            case 'clear':
                this.clearLogs();
                break;
            case 'export':
                this.exportLogs();
                break;
        }
        this.hideContextMenu();
    }

    // 复制日志到剪贴板
    copyLogsToClipboard() {
        const logs = Array.from(this.liveData.children).map(entry => {
            const timestamp = entry.querySelector('.timestamp')?.textContent || '';
            const message = entry.querySelector('.message')?.textContent || '';
            return `${timestamp} ${message}`;
        }).join('\n');

        navigator.clipboard.writeText(logs).then(() => {
            this.showToast('复制成功', '日志已复制到剪贴板', 'success');
        }).catch(() => {
            this.showToast('复制失败', '无法访问剪贴板', 'error');
        });
    }

    // 增强的模态框显示
    showModal(title, message, type = 'info', callback = null) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;

        // 根据类型设置样式
        this.modal.className = `modal ${type}`;

        if (callback) {
            this.modalCancel.style.display = 'inline-block';
            this.modalOk.onclick = () => {
                callback();
                this.hideModal();
            };
        } else {
            this.modalCancel.style.display = 'none';
            this.modalOk.onclick = () => this.hideModal();
        }

        this.modal.style.display = 'block';
    }

    // 更新连接状态显示
    updateConnectionStatus(status, message) {
        this.connectionStatus.textContent = message;
        this.connectionDot.className = `status-dot ${status}`;

        // 更新统计信息
        this.clientsCount.textContent = this.clients.length.toString();
        this.activeConnectionsCount.textContent = this.activeConnections.size.toString();

        // 更新系统信息
        this.updateSystemInfo();
    }

    // 重写addLog方法以支持消息计数
    addLog(type, message, data = null) {
        this.messageCount++;
        if (type === 'error') {
            this.errorCount++;
        }

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${type}`;
        logEntry.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="message">${message}</span>
        `;

        this.liveData.appendChild(logEntry);

        // 限制日志条数
        while (this.liveData.children.length > 1000) {
            this.liveData.removeChild(this.liveData.firstChild);
        }

        // 自动滚动
        if (this.autoScrollCheckbox && this.autoScrollCheckbox.checked) {
            this.liveData.scrollTop = this.liveData.scrollHeight;
        }

        // 保存到持久化存储
        this.savePersistedData();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TikTokMonitorApp();
});

// 点击模态框外部关闭
window.addEventListener('click', (event) => {
    const modal = document.getElementById('modal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});

// 页面卸载时保存数据
window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.savePersistedData();
    }
});

// 定期保存数据
setInterval(() => {
    if (window.app) {
        window.app.savePersistedData();
    }
}, 30000); // 每30秒保存一次
