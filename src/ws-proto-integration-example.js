/**
 * WebSocket Protobuf 集成示例
 * 展示如何将protobuf集成到现有的WebSocket通信中
 */

const WebSocketProtoHelper = require('./ws-proto-example');

class WebSocketWithProtobuf {
    constructor(serverUrl, context) {
        this.serverUrl = serverUrl;
        this.context = context;
        this.wsClient = null;
        this.isConnected = false;
        this.protoHelper = new WebSocketProtoHelper();
        
        // 是否使用protobuf（可以通过配置切换）
        this.useProtobuf = true;
    }

    /**
     * 连接到WebSocket服务器
     */
    connect() {
        const WebSocket = require('ws');
        this.wsClient = new WebSocket(this.serverUrl);

        this.wsClient.on('open', () => {
            console.log('✅ Connected to WebSocket server');
            this.isConnected = true;
            
            // 发送客户端信息
            this.sendClientInfo();
        });

        this.wsClient.on('message', (data) => {
            try {
                if (this.useProtobuf) {
                    // 使用protobuf解析消息
                    this.handleProtobufMessage(data);
                } else {
                    // 使用JSON解析消息（兼容模式）
                    const message = JSON.parse(data);
                    this.handleJsonMessage(message);
                }
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        });

        this.wsClient.on('close', () => {
            console.log('❌ Disconnected from WebSocket server');
            this.isConnected = false;
        });

        this.wsClient.on('error', (error) => {
            console.error('WebSocket error:', error);
        });
    }

    /**
     * 处理protobuf消息
     */
    handleProtobufMessage(buffer) {
        try {
            const message = this.protoHelper.deserializeServerMessage(buffer);
            const messageType = this.protoHelper.getMessageType(message);
            
            console.log('Received protobuf message:', messageType);
            
            switch (messageType) {
                case 'welcome':
                    this.handleWelcomeMessage(message.welcome);
                    break;
                case 'live_room_request':
                    this.handleLiveRoomRequest(message.liveRoomRequest);
                    break;
                case 'disconnect_room_request':
                    this.handleDisconnectRoomRequest(message.disconnectRoomRequest);
                    break;
                case 'get_status_request':
                    this.handleGetStatusRequest(message.getStatusRequest);
                    break;
                case 'server_broadcast':
                    this.handleServerBroadcast(message.serverBroadcast);
                    break;
                default:
                    console.log('Unknown protobuf message type:', messageType);
            }
        } catch (error) {
            console.error('Error handling protobuf message:', error);
        }
    }

    /**
     * 处理JSON消息（兼容模式）
     */
    handleJsonMessage(message) {
        console.log('Received JSON message:', message.type);
        
        switch (message.type) {
            case 'welcome':
                console.log('Server welcome:', message.message);
                break;
            case 'live_room_request':
                this.handleLiveRoomRequestJson(message);
                break;
            case 'disconnect_room_request':
                this.handleDisconnectRoomRequestJson(message);
                break;
            case 'get_status_request':
                this.handleGetStatusRequestJson(message);
                break;
            default:
                console.log('Unknown JSON message type:', message.type);
        }
    }

    /**
     * 发送客户端信息
     */
    sendClientInfo() {
        if (this.useProtobuf) {
            const message = this.protoHelper.createClientInfoMessage(this.context);
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.wsClient.send(buffer);
        } else {
            // JSON模式
            this.wsClient.send(JSON.stringify({
                type: 'client_info',
                context: this.context,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * 发送心跳消息
     */
    sendHeartbeat(totalConnections) {
        if (!this.isConnected) return;
        
        if (this.useProtobuf) {
            const message = this.protoHelper.createHeartbeatMessage(this.context, totalConnections);
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.wsClient.send(buffer);
        } else {
            // JSON模式
            this.wsClient.send(JSON.stringify({
                type: 'heartbeat',
                context: this.context,
                totalConnections: totalConnections,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * 发送直播事件数据
     */
    sendLiveEvents(connectionId, liveUrl, eventsData) {
        if (!this.isConnected) return;
        
        if (this.useProtobuf) {
            const message = this.protoHelper.createLiveEventsMessage(
                connectionId, liveUrl, eventsData, this.context
            );
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.wsClient.send(buffer);
        } else {
            // JSON模式
            this.wsClient.send(JSON.stringify({
                type: 'live_events',
                connectionId: connectionId,
                liveUrl: liveUrl,
                data: eventsData,
                timestamp: Date.now(),
                source: 'LiveTiktok',
                context: this.context
            }));
        }
    }

    /**
     * 发送直播间响应
     */
    sendLiveRoomResponse(requestId, success, connectionId, liveUrl, error = null) {
        if (!this.isConnected) return;
        
        if (this.useProtobuf) {
            const message = this.protoHelper.createLiveRoomResponseMessage(
                requestId, success, connectionId, liveUrl, error
            );
            const buffer = this.protoHelper.serializeClientMessage(message);
            this.wsClient.send(buffer);
        } else {
            // JSON模式
            this.wsClient.send(JSON.stringify({
                type: 'live_room_response',
                requestId: requestId,
                success: success,
                connectionId: connectionId,
                liveUrl: liveUrl,
                error: error,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * 处理欢迎消息
     */
    handleWelcomeMessage(message) {
        console.log('Server welcome:', message.message);
        console.log('Client ID:', message.clientId);
    }

    /**
     * 处理直播间请求
     */
    handleLiveRoomRequest(message) {
        console.log('Live room request:', message.liveUrl);
        console.log('Request ID:', message.requestId);
        
        // 模拟处理逻辑
        setTimeout(() => {
            // 发送成功响应
            this.sendLiveRoomResponse(
                message.requestId, 
                true, 
                'conn_' + Date.now(), 
                message.liveUrl
            );
        }, 1000);
    }

    /**
     * 处理断开直播间请求
     */
    handleDisconnectRoomRequest(message) {
        console.log('Disconnect room request:', message.connectionId);
        console.log('Request ID:', message.requestId);
    }

    /**
     * 处理获取状态请求
     */
    handleGetStatusRequest(message) {
        console.log('Get status request:', message.requestId);
        
        // 创建状态信息
        const statusInfo = {
            context: this.context,
            isConnected: this.isConnected,
            totalConnections: 1,
            connections: [],
            globalStats: {
                totalLikes: 0,
                totalWatching: 0,
                totalComments: 0,
                totalGifts: 0
            },
            timestamp: Date.now()
        };
        
        // 发送状态响应
        if (this.useProtobuf) {
            const responseMessage = this.protoHelper.createStatusResponseMessage(
                message.requestId, statusInfo
            );
            const buffer = this.protoHelper.serializeClientMessage(responseMessage);
            this.wsClient.send(buffer);
        } else {
            this.wsClient.send(JSON.stringify({
                type: 'status_response',
                requestId: message.requestId,
                status: statusInfo,
                timestamp: Date.now()
            }));
        }
    }

    /**
     * 处理服务器广播
     */
    handleServerBroadcast(message) {
        console.log('Server broadcast:', message.message);
    }

    /**
     * JSON模式的处理方法（兼容性）
     */
    handleLiveRoomRequestJson(message) {
        this.handleLiveRoomRequest(message);
    }

    handleDisconnectRoomRequestJson(message) {
        this.handleDisconnectRoomRequest(message);
    }

    handleGetStatusRequestJson(message) {
        this.handleGetStatusRequest(message);
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.wsClient) {
            this.wsClient.close();
        }
    }

    /**
     * 切换protobuf/JSON模式
     */
    setProtobufMode(enabled) {
        this.useProtobuf = enabled;
        console.log(`Protobuf mode: ${enabled ? 'enabled' : 'disabled'}`);
    }
}

// 使用示例
if (require.main === module) {
    const client = new WebSocketWithProtobuf('ws://localhost:8085', 'test-client');
    
    // 连接到服务器
    client.connect();
    
    // 定期发送心跳
    setInterval(() => {
        client.sendHeartbeat(1);
    }, 30000);
    
    // 5秒后发送测试事件
    setTimeout(() => {
        client.sendLiveEvents('test-conn', 'https://example.com/live', {
            events_data: [],
            like_total: 100,
            watching_total: 50
        });
    }, 5000);
}

module.exports = WebSocketWithProtobuf;
