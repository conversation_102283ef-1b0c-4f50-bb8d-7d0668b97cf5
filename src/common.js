
/// @secureBegin
function packCtlEvent(action, msg, live_url) {
    const _eventData = {};
    _eventData.msg_type = 5;
    _eventData.msg_type_str = 'control_msg';
    _eventData.control_msg = {};
    _eventData.control_msg.action = action;
    _eventData.control_msg.action_msg = msg;
    _eventData.control_msg.live_url = live_url;
    return _eventData;
}
function initEvents(context, live_platform_type) {
    return {
        events_data: [],
        timestamp: new Date().getTime(),
        live_platform_type: live_platform_type,
        context: context
    };
}


/// @secureEnd

module.exports = {
    packCtlEvent: packCtlEvent,
    initEvents: initEvents,
};
