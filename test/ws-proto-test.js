/**
 * WebSocket Protobuf 测试脚本
 * 验证protobuf消息的创建、序列化和反序列化功能
 */

const WebSocketProtoHelper = require('../src/ws-proto-example');

function testProtobufMessages() {
    console.log('🧪 开始测试 WebSocket Protobuf 功能...\n');
    
    const protoHelper = new WebSocketProtoHelper();
    let testsPassed = 0;
    let totalTests = 0;

    // 测试辅助函数
    function runTest(testName, testFunction) {
        totalTests++;
        try {
            console.log(`📋 测试: ${testName}`);
            testFunction();
            console.log(`✅ 通过: ${testName}\n`);
            testsPassed++;
        } catch (error) {
            console.error(`❌ 失败: ${testName}`);
            console.error(`   错误: ${error.message}\n`);
        }
    }

    // 测试1: 客户端信息消息
    runTest('ClientInfoMessage 创建和序列化', () => {
        const message = protoHelper.createClientInfoMessage('test-context');
        const buffer = protoHelper.serializeClientMessage(message);
        const decoded = protoHelper.deserializeClientMessage(buffer);
        
        if (!decoded.clientInfo) throw new Error('clientInfo 字段缺失');
        if (decoded.clientInfo.type !== 'client_info') throw new Error('type 字段不正确');
        if (decoded.clientInfo.context !== 'test-context') throw new Error('context 字段不正确');
        if (!decoded.clientInfo.timestamp) throw new Error('timestamp 字段缺失');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   消息类型: ${protoHelper.getMessageType(decoded)}`);
    });

    // 测试2: 心跳消息
    runTest('HeartbeatMessage 创建和序列化', () => {
        const message = protoHelper.createHeartbeatMessage('test-context', 5);
        const buffer = protoHelper.serializeClientMessage(message);
        const decoded = protoHelper.deserializeClientMessage(buffer);
        
        if (!decoded.heartbeat) throw new Error('heartbeat 字段缺失');
        if (decoded.heartbeat.type !== 'heartbeat') throw new Error('type 字段不正确');
        if (decoded.heartbeat.context !== 'test-context') throw new Error('context 字段不正确');
        if (decoded.heartbeat.totalConnections !== 5) throw new Error('totalConnections 字段不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   总连接数: ${decoded.heartbeat.totalConnections}`);
    });

    // 测试3: 直播间响应消息
    runTest('LiveRoomResponseMessage 创建和序列化', () => {
        const message = protoHelper.createLiveRoomResponseMessage(
            'req-123', true, 'conn-456', 'https://example.com/live'
        );
        const buffer = protoHelper.serializeClientMessage(message);
        const decoded = protoHelper.deserializeClientMessage(buffer);
        
        if (!decoded.liveRoomResponse) throw new Error('liveRoomResponse 字段缺失');
        if (decoded.liveRoomResponse.requestId !== 'req-123') throw new Error('requestId 字段不正确');
        if (decoded.liveRoomResponse.success !== true) throw new Error('success 字段不正确');
        if (decoded.liveRoomResponse.connectionId !== 'conn-456') throw new Error('connectionId 字段不正确');
        if (decoded.liveRoomResponse.liveUrl !== 'https://example.com/live') throw new Error('liveUrl 字段不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   请求ID: ${decoded.liveRoomResponse.requestId}`);
        console.log(`   成功状态: ${decoded.liveRoomResponse.success}`);
    });

    // 测试4: 直播事件消息
    runTest('LiveEventsMessage 创建和序列化', () => {
        const eventsData = {
            events_data: [
                { type: 'like', count: 10 },
                { type: 'comment', text: 'Hello!' }
            ],
            like_total: 100,
            watching_total: 50
        };
        
        const message = protoHelper.createLiveEventsMessage(
            'conn-789', 'https://example.com/live', eventsData, 'test-context'
        );
        const buffer = protoHelper.serializeClientMessage(message);
        const decoded = protoHelper.deserializeClientMessage(buffer);
        
        if (!decoded.liveEvents) throw new Error('liveEvents 字段缺失');
        if (decoded.liveEvents.connectionId !== 'conn-789') throw new Error('connectionId 字段不正确');
        if (decoded.liveEvents.source !== 'LiveTiktok') throw new Error('source 字段不正确');
        
        const parsedData = JSON.parse(decoded.liveEvents.data);
        if (parsedData.like_total !== 100) throw new Error('事件数据解析不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   连接ID: ${decoded.liveEvents.connectionId}`);
        console.log(`   事件数据大小: ${decoded.liveEvents.data.length} 字符`);
    });

    // 测试5: 错误消息
    runTest('ErrorMessage 创建和序列化', () => {
        const message = protoHelper.createErrorMessage(
            'Connection failed', 'conn-123', 'https://example.com/live', 'test-context'
        );
        const buffer = protoHelper.serializeClientMessage(message);
        const decoded = protoHelper.deserializeClientMessage(buffer);
        
        if (!decoded.error) throw new Error('error 字段缺失');
        if (decoded.error.error !== 'Connection failed') throw new Error('error 字段不正确');
        if (decoded.error.connectionId !== 'conn-123') throw new Error('connectionId 字段不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   错误信息: ${decoded.error.error}`);
    });

    // 测试6: 服务器消息 - 欢迎消息
    runTest('WelcomeMessage 创建和序列化', () => {
        const message = protoHelper.createWelcomeMessage('Welcome to server!', 'client-123');
        const buffer = protoHelper.serializeServerMessage(message);
        const decoded = protoHelper.deserializeServerMessage(buffer);
        
        if (!decoded.welcome) throw new Error('welcome 字段缺失');
        if (decoded.welcome.message !== 'Welcome to server!') throw new Error('message 字段不正确');
        if (decoded.welcome.clientId !== 'client-123') throw new Error('clientId 字段不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   欢迎消息: ${decoded.welcome.message}`);
    });

    // 测试7: 服务器消息 - 直播间请求
    runTest('LiveRoomRequestMessage 创建和序列化', () => {
        const options = { quality: 'high', autoConnect: true };
        const message = protoHelper.createLiveRoomRequestMessage(
            'https://example.com/live', 'req-456', options
        );
        const buffer = protoHelper.serializeServerMessage(message);
        const decoded = protoHelper.deserializeServerMessage(buffer);
        
        if (!decoded.liveRoomRequest) throw new Error('liveRoomRequest 字段缺失');
        if (decoded.liveRoomRequest.liveUrl !== 'https://example.com/live') throw new Error('liveUrl 字段不正确');
        if (decoded.liveRoomRequest.requestId !== 'req-456') throw new Error('requestId 字段不正确');
        
        const parsedOptions = JSON.parse(decoded.liveRoomRequest.options);
        if (parsedOptions.quality !== 'high') throw new Error('options 解析不正确');
        
        console.log(`   序列化大小: ${buffer.length} 字节`);
        console.log(`   直播间URL: ${decoded.liveRoomRequest.liveUrl}`);
    });

    // 测试8: 消息类型识别
    runTest('消息类型识别', () => {
        const clientInfo = protoHelper.createClientInfoMessage('test');
        const heartbeat = protoHelper.createHeartbeatMessage('test', 1);
        const welcome = protoHelper.createWelcomeMessage('hello', 'client');
        
        if (protoHelper.getMessageType(clientInfo) !== 'client_info') {
            throw new Error('client_info 类型识别失败');
        }
        if (protoHelper.getMessageType(heartbeat) !== 'heartbeat') {
            throw new Error('heartbeat 类型识别失败');
        }
        if (protoHelper.getMessageType(welcome) !== 'welcome') {
            throw new Error('welcome 类型识别失败');
        }
        
        console.log(`   ✓ client_info 类型识别正确`);
        console.log(`   ✓ heartbeat 类型识别正确`);
        console.log(`   ✓ welcome 类型识别正确`);
    });

    // 测试9: 性能对比 (protobuf vs JSON)
    runTest('性能对比测试', () => {
        const testData = {
            type: 'live_events',
            connectionId: 'conn-performance-test-12345',
            liveUrl: 'https://www.tiktok.com/@example/live',
            data: {
                events_data: Array(100).fill().map((_, i) => ({
                    type: 'like',
                    userId: `user-${i}`,
                    timestamp: Date.now() + i,
                    count: Math.floor(Math.random() * 10)
                })),
                like_total: 5000,
                watching_total: 1200,
                comment_total: 800
            },
            timestamp: Date.now(),
            source: 'LiveTiktok',
            context: 'performance-test-context'
        };

        // JSON 序列化
        const jsonStart = Date.now();
        const jsonString = JSON.stringify(testData);
        const jsonTime = Date.now() - jsonStart;
        const jsonSize = Buffer.from(jsonString).length;

        // Protobuf 序列化
        const protoStart = Date.now();
        const protoMessage = protoHelper.createLiveEventsMessage(
            testData.connectionId, testData.liveUrl, testData.data, testData.context
        );
        const protoBuffer = protoHelper.serializeClientMessage(protoMessage);
        const protoTime = Date.now() - protoStart;
        const protoSize = protoBuffer.length;

        console.log(`   JSON 大小: ${jsonSize} 字节, 时间: ${jsonTime}ms`);
        console.log(`   Protobuf 大小: ${protoSize} 字节, 时间: ${protoTime}ms`);
        console.log(`   大小压缩率: ${((jsonSize - protoSize) / jsonSize * 100).toFixed(1)}%`);
        
        if (protoSize >= jsonSize) {
            console.log(`   ⚠️  注意: 对于这个测试数据，protobuf 并未显著减少大小`);
        }
    });

    // 输出测试结果
    console.log('📊 测试结果汇总:');
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过测试: ${testsPassed}`);
    console.log(`   失败测试: ${totalTests - testsPassed}`);
    console.log(`   成功率: ${((testsPassed / totalTests) * 100).toFixed(1)}%`);
    
    if (testsPassed === totalTests) {
        console.log('\n🎉 所有测试通过！WebSocket Protobuf 功能正常工作。');
    } else {
        console.log('\n⚠️  部分测试失败，请检查实现。');
    }
}

// 运行测试
if (require.main === module) {
    testProtobufMessages();
}

module.exports = { testProtobufMessages };
