const { app, BrowserWindow } = require('electron');
const LiveWebSocketBridge = require('./src/live-websocket-bridge');

let mainWindow;
let liveWebSocketBridge;
// 1
// 创建主窗口
async function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1920,
        height: 1080,
    });

    // 可选：打开开发者工具
    // mainWindow.webContents.openDevTools();

    console.log('Main window created');
}

// 启动LiveWebSocketBridge客户端模式
function startLiveWebSocketBridgeClient() {
    // 创建LiveWebSocketBridge实例（客户端模式）
    // 参数：context, wsPort, test, mode
    liveWebSocketBridge = new LiveWebSocketBridge('websocket-client-demo-context', 8087, false);

    // 设置远程服务器地址（可选，默认为 ws://localhost:8084）
    // liveWebSocketBridge.setServerUrl('ws://your-remote-server:8084');

    // 启动客户端，连接到远程服务器
    liveWebSocketBridge.startServer();

    console.log('WebSocket client started');
    console.log('Connecting to remote server...');
    console.log('');
    console.log('Client is now ready to:');
    console.log('1. Receive live room connection requests from the server');
    console.log('2. Create independent Electron windows for each live room');
    console.log('3. Send live events and logs back to the server');
    console.log('4. Handle server commands for room management');

    // 定期发送心跳和状态信息（每30秒）
    setInterval(() => {
        if (liveWebSocketBridge.isConnected) {
            liveWebSocketBridge.updateHeartBeat(liveWebSocketBridge.context, liveWebSocketBridge.connections.size);
        }
    }, 30000);

    // 5秒后发送一个测试消息
    setTimeout(() => {
        liveWebSocketBridge.broadcast({
            message: 'LiveWebSocketBridge client is ready to receive server commands',
            architecture: 'Each live room will create an independent Electron window',
            capabilities: {
                live_room_request: 'Can connect to live rooms requested by server',
                disconnect_room_request: 'Can disconnect from live rooms as requested by server',
                get_status_request: 'Can provide status information to server',
                live_events: 'Will send live events to server in real-time',
                logs: 'Will send log messages to server'
            }
        });
    }, 5000);
}

// Electron应用事件处理
app.whenReady().then(() => {
    createWindow();
    startLiveWebSocketBridgeClient();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (liveWebSocketBridge) {
            liveWebSocketBridge.release();
        }
        app.quit();
    }
});

app.on('before-quit', () => {
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('WebSocket Client Demo Application Starting...');
console.log('This demo will:');
console.log('1. Create an Electron window');
console.log('2. Connect to a remote WebSocket server');
console.log('3. Wait for server commands to connect to live rooms');
console.log('4. Create independent windows for each live room');
console.log('5. Send live events and logs back to the server');
console.log('');
console.log('Architecture: Client -> Server (reversed from original)');
console.log('The client connects to a remote server and receives commands');
console.log('Each live room creates its own independent Electron window');
