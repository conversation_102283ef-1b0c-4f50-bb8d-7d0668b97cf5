syntax = "proto3";
package tiktok.priv;
// npm-async-975150c9-0b92b196.4667be002e6f09325f9b

// msg 数据包二层解密
message Response {
    repeated Message messages = 1;
    string cursor = 2;
    int64 fetchInterval = 3;
    int64 now = 4;
    string internalExt = 5;
    int32 fetchType = 6;
    map<string, string> routeParams = 7;
    int64 heartbeatDuration = 8;
    bool needAck = 9;
    string pushServer = 10;
    bool is_first = 11;
    string history_comment_cursor = 12;
    bool history_no_more = 13;
}
message Message{
    string method = 1;
    bytes payload = 2;
    int64 msgId = 3;
    int32 msgType = 4;
    int64 offset = 5;
    bool is_history = 6;
}

// hb 数据包二层解密, 未测试
message HeartbeatPacket {
    repeated WrdsKeyVersion wrdsKeyVersions = 1;
    int64 roomID = 2;
}
message WrdsKeyVersion {
    string syncKey = 1;
    int64 version = 2;
}
