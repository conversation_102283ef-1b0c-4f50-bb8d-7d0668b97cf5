syntax = "proto3";
package tiktok.webcast;
import "tiktok_webcast_data.proto";

// ========================= Message 数据解析 =========================

message GiftMessage{
    Common common = 1;
    int64 gift_id = 2;
    int64 fan_ticket_count = 3;
    int64 group_count = 4;
    int64 repeat_count = 5;
    int64 combo_count = 6;
    User user = 7;
    User to_user = 8;
    int32 repeat_end = 9;
    TextEffect text_effect = 10;
    int64 group_id = 11;
    int64 income_taskgifts = 12;
    int64 room_fan_ticket_count = 13;
    GiftIMPriority priority = 14;
    GiftStruct gift = 15;
    string log_id = 16;
    int64 send_type = 17;
    PublicAreaCommon public_area_common = 18;
    Text tray_display_text = 19;
    int64 banned_display_effects = 20;
    GiftTrayInfo tray_info = 21;
    string monitor_extra = 22;
    GiftMonitorInfo monitor_info = 23;
    int64 color_id = 24;
    bool is_first_sent = 25;
    Text display_text_for_anchor = 26;
    Text display_text_for_audience = 27;
    string order_id = 28;
    GiftsInBox gifts_in_box = 29;
    MsgFilter msg_filter = 30;
    repeated LynxGiftExtra lynx_extra = 31;
    UserIdentity user_identity = 32;
    MatchInfo match_info = 33;
    LinkmicGiftExpressionStrategy linkmic_gift_expression_strategy = 34;
    FlyingMicResources flying_mic_resources = 35;
    bool disable_gift_tracking = 36;
    AssetStruct asset = 37;
    GiftMessageVersion version = 38;
    repeated SponsorshipInfo sponsorship_info = 39;
    FlyingMicResources flying_mic_resources_v2 = 40;
    PublicAreaMessageCommon public_area_message_common = 41;
    string signature = 42;
    string signature_version = 43;
    bool multi_generate_message = 44;
    string to_member_id = 45;
    int64 to_member_id_int = 46;
    string to_member_nickname = 47;
    InteractiveGiftInfo interactive_gift_info = 48;

    // webcast.im.GiftMessage.TextEffect
    message TextEffect {
        Detail portrait = 1;
        Detail landscape = 2;

        message Detail {
            Text text = 1;
            int32 textFontSize = 2;
            Image background = 3;
            int32 start = 4;
            int32 duration = 5;
            int32 x = 6;
            int32 y = 7;
            int32 width = 8;
            int32 height = 9;
            int32 shadowDx = 10;
            int32 shadowDy = 11;
            int32 shadowRadius = 12;
            string shadowColor = 13;
            string strokeColor = 14;
            int32 strokeWidth = 15;
        }
    }
    message SponsorshipInfo{
        int64 gift_id = 1;
        int64 sponsor_id = 2;
        bool light_gift_up = 3;
        string unlighted_gift_icon = 4;
        string gift_gallery_detail_page_scheme_url = 5;
        bool gift_gallery_click_sponsor = 6;
        bool become_all_sponsored = 21;
    }

    message InteractiveGiftInfo {
        int64 cross_screen_delay = 1;
        int64 cross_screen_role = 2;
        GiftMessageIgnoreConfig ignore_config = 3;
        int64 uniq_id = 4;
        int64 to_user_team_id = 5;
    }
    enum GiftMessageIgnoreConfig{
        GIFT_MESSAGE_IGNORE_CONFIG_NOT_IGNORE = 0;
        GIFT_MESSAGE_IGNORE_CONFIG_IGNORE_TRAY = 1;
        GIFT_MESSAGE_IGNORE_CONFIG_IGNORE_PSM = 2;
        GIFT_MESSAGE_IGNORE_CONFIG_IGNORE_TRAY_AND_PSM = 3;
    }
}
message DoodleGiftMessage{
    Common common = 1;
    int64 giftId = 2;
    int64 fanTicketCount = 3;
    User user = 4;
    User toUser = 5;
    string compose = 6;
    int64 roomFanTicketCount = 7;
    GiftIMPriority priority = 8;
    string logId = 9;
    PublicAreaCommon publicAreaCommon = 10;
    Text trayDisplayText = 11;
    GiftTrayInfo trayInfo = 12;
}
message UpdateFanTicketMessage{
    Common common = 1;
    string roomFanTicketCountText = 2;
    int64 roomFanTicketCount = 3;
    bool forceUpdate = 4;
}
message BindingGiftMessage{
    GiftMessage msg = 1;
    Common common = 2;
}
message AssetEffectUtilMessage{
    Common common = 1;
    int64 assetId = 2;
    int64 priority = 3;
    int64 price = 4;
    Text text = 5;
    int64 textWidth = 6;
    int64 textHeitht = 7;
    string tracking = 8;
    string dressId = 9;
    repeated EffectUtilImageInfo images = 10;
    repeated EffectUtilTextInfo texts = 20;
    string finishSelfSchema = 21;
    map<string, TextPiece> pieceValues = 22;
}
message GiftUpdateMessage{
    Common common = 1;
    int32 updateType = 2;
    repeated int64 updateGiftIds = 3;
    repeated int64 updateAssetIds = 4;
    GiftSortStrategy giftSortStrategy = 5;
}
// 礼物投票
message GiftVoteMessage{
    Common common = 1;
    int64 msgType = 2;
    int64 voteId = 3;
    repeated GiftVoteResult results = 4;
    int64 finishTime = 5;
    int64 currentTime = 6;
    int64 voteType = 7;
    string extra = 8;
    message Result{
        string text = 1;
        int64 count = 2;
        Image icon = 3;
        int64 giftId = 4;
        string name = 5;
        int64 diamondCount = 6;
        int64 giftType = 7;
        string countStr = 8;
    }
}
message RoomMessage{
    Common common = 1;
    string content = 2;
    bool supportLandscape = 3;
    int64 source = 4;
    Image icon = 5;
    string scene = 6;
    bool is_welcome = 7;
    PublicAreaMessageCommon public_area_common = 8;
    int64 show_duration_ms = 9;
    string sub_scene = 10;
}
// 聊天相关消息
message ChatMessage{
    Common common = 1;
    User user = 2;
    string content = 3;
    bool visible_to_sender = 4;
    Image background_image = 5;
    string full_screen_text_color = 6;
    Image background_image_v2 = 7;
    PublicAreaCommon public_area_common = 9;
    Image gift_image = 10;
    int32 input_type = 11;
    User at_user = 12;
    repeated EmoteWithIndex emotes = 13;
    string content_language = 14;
    MsgFilter msg_filter = 15;
    int32 quick_chat_scene = 16;
    int32 community_flagged_status = 17;
    UserIdentity user_identity = 18;
    repeated CommentQualityScore comment_quality_scores = 19;
    repeated CommentTag comment_tag = 20;
    PublicAreaMessageCommon public_area_message_common = 21;
    int64 screen_time = 22;
    string signature = 23;
    string signature_version = 24;
    string ec_streamer_key = 25;
}
message CommentQualityScore{
    string version = 1;
    int64 score = 2;
}
enum CommentTag{
    COMMENT_TAG_NORMAL = 0;
    COMMENT_TAG_CANDIDATE = 1;
    COMMENT_TAG_OVERAGE = 2;
}
message EmojiChatMessage{
    Common common = 1;
    User user = 2;
    int64 emojiId = 3;
    Text emojiContent = 4;
    string defaultContent = 5;
    Image backgroundImage = 6;
    bool fromIntercom = 7;
    bool intercomHideUserCard = 8;
    PublicAreaCommon publicAreaCommon = 9;
}
message FriendChatMessage{
    Common common = 1;
    User user = 2;
    string content = 3;
}
message ChatCarnivalMessage{
    Common common = 1;
    int64 notifyType = 2;
    int64 anchorStartTimestamp = 3;
    int64 anchorStartSecond = 4;
    int64 userStartTimestamp = 5;
    int64 userStartSecond = 6;
    int64 duration = 7;
    string backgroundColor = 8;
    Image titleImage = 9;
    string preContent = 10;
    string content = 11;
    int64 orientations = 12;
    int64 moveSpeed = 13;
    repeated ChatItem chatStyle = 14;
    repeated EggItem eggStyle = 15;
    int64 wordOrientation = 16;
    int64 moveDirection = 17;
    repeated int64 eggShowRange = 18;
    repeated int64 sizeAndAlpha = 19;
    repeated int64 verticalOffset = 20;
    int64 path = 21;
    string wordColor = 22;
    int64 showWebp = 23;
    int64 usePrivilegeRegion = 24;
    int64 canOpenContour = 25;
    int64 supportShowType = 26;
    map<string, string> extra = 27;
}
message SocialMessage{
    Common common = 1;
    User user = 2;
    int64 shareType = 3;
    int64 action = 4;   // 关注主播 action == 1, 分享直播 action == 3
    string shareTarget = 5;
    int64 followCount = 6;  // 粉丝总数
    int64 share_display_style = 7;
    int64 share_count = 8;
    PublicAreaMessageCommon public_area_message_common = 9;
    string signature = 10;
    string signature_version = 11;
    int64 show_duration_ms = 12;
}
message SunDailyRankMessage{
    Common common = 1;
    string content = 2;
    int64 duration = 3;
    string afterContent = 4;
    int64 messageType = 5;
    string schema = 6;
    string extra = 7;
    string style = 8;
    Text afterDisplayText = 9;
    int64 rank = 10;
    string richContent = 11;
    string userSideContent = 12;
    int64 contentType = 13;
    string cityCode = 14;
    int32 rankStage = 15;   // 0 -> TYPE_NORMAL, 1 -> TYPE_WARMUP, 2 -> TYPE_SPRINT
    repeated RankInfo ranks = 16;
    int64 beginTime = 17;
    int64 deltaTime = 18;
    Text entranceAppearingText = 19;
    Text displayTextV2 = 20;
    Text afterDisplayTextV2 = 21;
    int64 durationV2 = 22;
    message RankInfo{
        int64 rankType = 1;
        int64 rank = 2;
    }
}
message GuestBattleMessage{
    Common common = 1;
    int64 msgType = 2;
    GuestBattleInfo battleInfo = 3;
    string finishToast = 4;
    GuestBattleUpdateContent updateContent = 100;
    GuestBattleFinishContent finishContent = 101;
}
message ImDeleteMessage{
    Common common = 1;
    repeated int64 deleteMsgIds = 2;
}
//TODO: more msg
message RoomRankMessage{
    Common common = 1;
    repeated RoomRank ranks = 2;
    message RoomRank{
        User user = 1;
        string score_str = 2;
        bool profile_hidden = 3;
    }
}
message RoomStatsMessage{
    Common common = 1;
    string displayShort = 2;
    string displayMiddle = 3;
    string displayLong = 4;
    int64 displayValue = 5;
    int64 displayVersion = 6;
    bool incremental = 7;
    bool isHidden = 8;
    int64 total = 9;
    int64 displayType = 10;
}
message MemberMessage{
    Common common = 1;
    User user = 2;
    int64 member_count = 3;
    User operator = 4;
    bool is_set_to_admin = 5;
    bool is_top_user = 6;
    int64 rank_score = 7;
    int64 top_user_no = 8;
    int64 enter_type = 9;
    int64 action = 10;
    string action_description = 11;
    int64 user_id = 12;
    EffectConfig effect_config = 13;
    string pop_str = 14;
    EffectConfig enter_effect_config = 15;
    Image background_image = 16;
    Image background_image_v2 = 17;
    Text anchor_display_text = 18;
    string client_enter_source = 19;
    string client_enter_type = 20;
    string client_live_reason = 21;
    int64 action_duration = 22;
    string user_share_type = 23;
    DisplayStyle display_style = 24;
    map<int32, int32> admin_permissions = 25;
    int32 kick_source = 26;
    int64 allow_preview_time = 27;
    int64 last_subscription_action = 28;
    PublicAreaMessageCommon public_area_message_common = 29;
    int64 live_sub_only_tier = 30;
    int64 live_sub_only_month = 31;
    string ec_streamer_key = 32;
    int64 show_wave = 33;
    WaveAlgorithmData wave_algorithm_data = 34;
    enum DisplayStyle{
        DisplayStyleNormal = 0;
        DisplayStyleStay = 1;
        DisplayStyleChat = 2;
    }
    message WaveAlgorithmData{
        string algorithm_version = 1;
        bool is_alg_hit = 2;
        string predict_score = 3;
        bool is_rewatch = 4;
        bool is_follow = 5;
    }
    message EffectConfig{
        int64 type = 1;
        Image icon = 2;
        int64 avatar_pos = 3;
        Text text = 4;
        Image text_icon = 5;
        int32 stay_time = 6;
        int64 anim_asset_id = 7;
        Image badge = 8;
        repeated int64 flex_setting_array = 9;
    }
}
message LikeMessage{
    Common common = 1;
    int64 count = 2;
    int64 total = 3;
    int64 color = 4;
    User user = 5;
    string icon = 6;
    repeated Image icons = 7;
    repeated SpecifiedDisplayText specified_display_text = 8;
    int64 effect_cnt = 9;
    LikeEffect like_effect = 10;
    PublicAreaMessageCommon public_area_message_common = 11;
    int64 room_message_heat_level = 12;
}
message SpecifiedDisplayText{
    int64 uid = 1;
    Text display_text = 2;
}
message LikeEffect{
    int64 version = 1;
    int64 effect_cnt = 2;
    int64 effect_interval_ms = 3;
    int64 level = 4;
}
message PublicAreaMessageCommon{
    int64 scroll_gap_count = 1;
    int64 anchor_scroll_gap_count = 2;
    bool release_to_scroll_area = 3;
    bool anchor_release_to_scroll_area = 4;
    bool is_anchor_marked = 5;
    CreatorSuccessInfo creator_success_info = 6;
    PortraitInfo portrait_info = 7;
    UserInteractionInfo user_interaction_info = 8;
    int64 admin_fold_type = 9;
    message CreatorSuccessInfo {
        repeated TagItem tags = 1;
        Topic topic = 2;

        message TagItem {
            TagType tag_type = 1;
            Text tag_text = 2;
        }

        message Topic {
            TopicActionType topic_action_type = 1;
            Text topic_text = 2;
            Text topic_tips = 3;
        }
    }
    message PortraitInfo {
        repeated UserMetrics user_metrics = 1;
        repeated PortraitTag portrait_tag = 2;

        message UserMetrics {
            UserMetricsType type = 1;
            string metrics_value = 2;

            enum UserMetricsType {
                USER_METRICS_TYPE_UNKNOWN = 0;
                USER_METRICS_TYPE_GRADE = 1;
                USER_METRICS_TYPE_SUBSCRIBE = 2;
                USER_METRICS_TYPE_FOLLOW = 3;
                USER_METRICS_TYPE_FANSCLUB = 4;
                USER_METRICS_TYPE_TOPVIEWER = 5;
                USER_METRICS_TYPE_GIFT = 6;
            }
        }

        message PortraitTag {
            string tag_id = 1;
            int64 priority = 2;
            string show_value = 3;
            string show_args = 4;
        }
    }

    message UserInteractionInfo {
        int64 like_cnt = 1;
        int64 comment_cnt = 2;
        int64 share_cnt = 3;
    }
}
message RoomUserSeqMessage{
    Common common = 1;
    repeated Contributor ranks = 2;
    int64 total = 3;
    string pop_str = 4;
    repeated Contributor seats = 5;
    int64 popularity = 6;
    int64 total_user = 7;
    int64 anonymous = 8;
    message Contributor{
        int64 score = 1;
        User user = 2;
        int64 rank = 3;
        int64 delta = 4;
    }
}
message ScreenChatMessage{
    Common common = 1;
    User user = 2;
    int64 screen_chat_type = 3;
    string content = 4;
    int64 priority = 5;
    Effect effect = 6;
    Image background_image = 7;
    Effect effect_v2 = 8;
    Image background_image_v2 = 9;
    PublicAreaCommon public_area_common = 10;
    message Effect {
        FlexImageStruct icon = 1;
        Image avatarIcon = 2;
    }
}
message ShortTouchAreaMessage{
    Common common = 1;
    MessageType messageType = 2;
    ShortTouchArea shortTouchAreaData = 3;
    enum MessageType{
        UnknownMessageType = 0;
        Refresh = 1;
        Destroy = 2;
    }
}
message FansclubStatisticsMessage{
    Common common = 1;
    string name = 2;
    int64 fansCount = 3;
}
message FansclubMessage{
    Common common = 1;
    int32 action = 2;
    string content = 3;
    User user = 4;
    UpgradePrivilege upgradePrivilege = 5;
    message UpgradePrivilege{
        string content = 1;
        string description = 2;
        int32 buttonType = 3;
    }
}
message FansclubReviewMessage{
    Common common = 1;
    int32 action = 2;
    string content = 3;
}
message FansclubGuideMessage{
    Common common = 1;
    Text title = 2;
    Text subTitle = 3;
}
message InteractionPluginGiftMessage{
    Common common = 1;
    repeated string gifts = 2;
}
message TopEffectMessage{
    Common common = 1;
    int64 assetId = 2;
    int64 priority = 3;
    int32 action = 4;
    string scene = 5;
    int64 endTime = 6;
    int64 fadeInDuration = 7;
    int64 fadeOutDuration = 8;
    repeated EffectImageInfo images = 10;
    repeated EffectTextInfo texts = 20;
}
message AtmosphereEffectMessage{
    Common common = 1;
    int64 assetId = 2;
    int64 partnerAssetId = 3;
    int64 priority = 4;
    int32 action = 5;
    string scene = 6;
    int64 endTime = 7;
    string actvityInformation = 8;
    int64 partnerHotsoonAssetId = 9;
}
message ControlMessage {
    Common common = 1;
    int64 action = 2;
    string tips = 3;
    Extra extra = 4;
    PerceptionDialogInfo perception_dialog = 5;
    Text perception_audience_text = 6;
    PunishEventInfo punish_info = 7;
    Text float_text = 8;
    int32 float_style = 9;
    message Extra {
        string ban_info_url = 1;
        int64 reason_no = 2;
        Text title = 3;
        Text violation_reason = 4;
        Text content = 5;
        Text got_it_button = 6;
        Text ban_detail_button = 7;
        string source = 8;
    }
}


message RoomDataSyncMessage{
    Common common = 1;
    int64 roomID = 2;
    string syncKey = 3;
    int64 version = 4;
    bytes payload = 5;
    string bizLogID = 6;
}
message RanklistHourEntranceMessage{
    Common common = 1;
    RanklistHourEntrance info = 2;
}
message FeedbackCardMessage {
    Common common = 1;
    int64 questionnaireId = 2;
    int64 scatterMills = 3;
}
message LinkMicMethod {
    Common common = 1;
    int64 messageType = 2;
    string accessKey = 3;
    int64 anchorLinkmicId = 4;
    int64 userId = 5;
    int64 fanTicket = 6;
    int64 totalLinkmicFanTicket = 7;
    int64 channelId = 8;
    int64 layout = 9;
    int64 vendor = 10;
    int64 dimension = 11;
    string theme = 12;
    int64 inviteUid = 13;
    int64 answer = 14;
    int64 startTime = 15;
    int64 duration = 16;
    repeated UserScores userScores = 17;
    int64 matchType = 18;
    bool win = 19;
    string prompts = 20;
    int64 toUserId = 21;
    map<int64, ContributorList> contributors = 22;
    int64 linkmicLayout = 23;
    int64 fromUserId = 24;
    string tips = 25;
    int64 startTimeMs = 26;
    int32 confluenceType = 27;
    int64 fromRoomId = 28;
    int64 inviteType = 29;
    int64 subType = 30;
    RivalExtraInfo inviterRivalExtra = 31;
    string rtcExtInfo = 32;
    string rtcAppId = 33;
    string appId = 34;
    string appSign = 35;
    string rtcAppSign = 36;
    string anchorLinkmicIdStr = 37;
    int64 rivalAnchorId = 38;
    int64 rivalLinkmicId = 39;
    string rivalLinkmicIdStr = 40;
    bool showPopup = 41;
    int64 secInviteUid = 42;
    int64 scene = 43;
    int64 secApplyUid = 44;
    repeated User linkedUsers = 45;
    string secFromUserId = 46;
    int32 replyType = 47;
    string replyPrompts = 48;
    string secToUserId = 49;
    InvitorInfo invitorInfo = 50;
    bool rtcJoinChannel = 51;
    int32 subScene = 52;
    bool supportUpdateLinkType = 53;
    int32 linkType = 54;
    int32 source = 55;
    AnchorLinkmicInfo backupAnchorLinkmicInfo = 56;
    repeated int32 playModes = 57;
    repeated ListUser linkedListUsers = 58;
    repeated LinkmicPositionItem lockedPositions = 59;
    int32 endReason = 60;
    MultiRtcInfo multiRtcInfo = 61;
    MultiLiveCoreInfo multiLiveCoreInfo = 62;
    int32 layoutConfigVersion = 63;
    int32 controlType = 64;
    BanUser banAnchorInfo = 65;
    uint64 version = 66;
    JoinTeamfightInfo joinTeamfightInfo = 67;
    int64 paidCount = 68;
    int64 linkDuration = 69;
    string liveCoreExtInfo = 70;
    GameInviteInfo gameInviteInfo = 71;
    int32 uiLayout = 72;
    Text inviteMessage = 73;
    MultiChannelInfo multiChannelInfo = 74;
    int32 kickOutSource = 75;
    map<int64, RoomLinkerContent> linkerContentMap = 76;
    string functionType = 77;
    MsgBoardItemInfo msgBoardItem = 78;
    string fromUserLinkmicIdStr = 79;
    string ackMessage = 80;
    string initSource = 81;
    string scoreUpdateLogId = 82;
    int32 switchPlayMode = 83;
    int32 updateMessageFrom = 84;
    LinkmicMediaInfo guestLinkmicInfo = 85;
    MCUContent mcuContent = 86;
    BreakthroughInfo breakthroughInfo = 87;
    LinkerBaseInfo linkerBaseInfo = 88;
    AudienceActionSource actionSource = 89;
    BattleBarConfig battleBarConfig = 90;
    User fromUser = 91;
    map<int64, AuxiliaryScoreInfo> auxiliaryScoreInfos = 92;
    string linkerSessionId = 93;
    string remoteLinkerSessionId = 94;
    LinkGameInfo linkGameInfo = 95;
    Image inviteImage = 96;
    string invitePrompt = 97;
    LinkmicJoinChannelData joinChannelData = 98;
    bool switchToSilence = 99;
    bool isAutoJoin = 100;
    int64 gameBarrageLinkerId = 101;
    string gameBarrageLinkerFinishReason = 102;
    ChorusCDNInfo chorusCdnInfo = 103;
    int64 battleId = 104;
    string openId = 5000;
    string inviteOpenId = 5001;
    string toOpenId = 5002;
    string fromOpenId = 5003;
    string rivalAnchorOpenId = 5004;
    message UserScores {
        int64 score = 1;
        int64 userId = 2;
        int64 weeklyRank = 3;
        string scoreRelativeText = 4;
        bool isLargePkScore = 5;
        bool roomLikeTrigger = 6;
        string scoreBlurText = 7;
        int64 battleRank = 8;
        bool newScoreOpen = 9;
        string multiPkTeamScoreText = 10;
        int64 multiPkTeamScore = 11;
        int64 multiPkTeamRank = 12;
        bool isMultiPkRelativeText = 13;
        string curAddScoreText = 14;
        int64 buffScoreRatio = 15;
        string openId = 5000;
    }
    message ContributorList {
        repeated Contributor contributorList = 1;
    }
    message Contributor{
        int64 score = 1;
        int64 rank = 2;
        int64 userId = 3;
        User user = 4;
        string openId = 5000;
    }
    message InvitorInfo {
        string invitorNickName = 1;
        Image invitorAvatar = 2;
    }
}
message AudioChatMessage{
    Common common = 1;
    User user = 2;
    string content = 3;
    string audioUrl = 4;
    int64 audioDuration = 5;
    PublicAreaCommon publicAreaCommon = 6;
    Text rtfContent = 7;
}
message InRoomBannerMessage{
    Common common = 1;
    string extra = 2;
    Position position = 3;
    ActionType actionType = 4;
    string containerUrl = 5;
    string lynxContainerUrl = 6;
    ContainerType containerType = 7;
    OpType opType = 8;
    enum Position {
        UNDEFINED = 0;
        BOTTOM_RIGHT = 1;
        TOP_RIGHT = 2;
        TOP_LEFT = 3;
        BOTTOM_LEFT = 4;
        ACTIVITY_TOP_RIGHT = 5;
        ACTIVITY_VS_INTERACTIVE = 6;
        ACTIVITY_INTERACTIVE = 7;
        ACTIVITY_VS_BOTTOM_RIGHT = 8;
        RETAIN_CONSULT_BOTTOM_RIGHT = 9;
    }
    enum ActionType{
        Default = 0;
        REFRESH_BANNER = 1;
    }
    enum ContainerType{
        Webview = 0;
        Lynx = 1;
    }
    enum OpType{
        Delete = 0;
        Add = 1;
    }
}
message RoomStreamAdaptationMessage {
    Common common = 1;
    int32 adaptation_type = 2;
    float adaptation_height_ratio = 3;
    float adaptation_body_center_ratio = 4;
    float adaptation_content_top_ratio = 5;
    float adaptation_content_bottom_ratio = 6;
}
message HotRoomMessage{
    Common common = 1;
    HotRoomInfo info = 2;
}
message HotChatMessage{
    Common common = 1;
    string title = 2;
    string content = 3;
    repeated int64 num = 4;
    int64 duration = 5;
    repeated int64 show_duration = 6;
    int64 sequence_id = 7;
    repeated string hot_list = 8;
    Text rtf_content = 9;
    int64 chat_content_type = 10;
    map<string, string> extra = 200;
}
message ActivityEmojiGroupsMessage{
    Common common = 1;
    EffectiveActivityEmojiGroup activity_emoji_groups = 2;
}
message HotWord {
    int64 sequence_id = 1;
    int64 repeat_count = 2;
    string display_count = 3;
    bool mock_counter = 4;
    string pre_content = 5;
    Text rtf_content = 6;
    int64 show_duration = 7;
}
message NewHotGatherMessage{
    Common common = 1;
    HotWord hot_word_list = 2;
    int64 switch_duration = 3;
}
message LightGiftMessage {
    Common common = 1;
    int64 group_count = 2;
    int64 repeat_count = 3;
    int64 combo_count = 4;
    int64 to_user_id = 5;
    GiftIMPriority priority = 6;
    GiftInfo gift_info = 7;
    GiftLiteTrayInfo tray_info = 8;
    int64 send_type = 9;
    int64 count = 10;
    string diy_item_info = 11;
    int64 banned_display_effects = 12;
    GiftStruct gift_struct = 13;
    string to_openid = 5000;
}
message GiftSortMessage {
    Common common = 1;
    int32 message_type = 2;
    int64 min_consume_level = 3;
    GiftSortStrategy scene_insert_strategy = 4;
}
message GiftTouchMessage{
    Common common = 1;
    string scene = 2;
    GiftPanelTopBar gift_panel_topbar = 3;
    ComboTrayInfo combo_tray_info = 4;
    int64 now_millis = 5;
    int32 display_position = 6;
    User to_user = 7;
}
message InteractEffectMessage {
    Common common = 1;
    int64 effect_id = 2;
    string extra = 3;
    string tea_log = 4;
    int64 message_type = 5;
    int64 arg1 = 6;
    int64 arg2 = 7;
    string arg8 = 8;
}
message ChatLikeMessage {
    Common common = 1;
    repeated ChatLikeCount total_msg_data = 2;
}
message CommonToastMessage {
    Common common = 1;
    bool discardable = 2;
    bool immediate = 3;
    int64 duration = 4;
    string text_color = 5;
    string background_color_start = 6;
    string background_color_end = 7;
    int64 position = 8;
    Image top_img = 9;
    int64 top_img_width = 10;
    int64 top_img_height = 11;
    bool show_mongolia_layer = 12;
}
message FreeGiftMessage {
    Common common = 1;
    User user = 2;
    FreeGift free_gift = 3;
}
message GameGiftMessage {
    Common common = 1;
    int64 gift_id = 2;
    int64 fan_ticket_count = 3;
    User user = 4;
    User to_user = 5;
    string normalContent = 6;
    MonkeyData monkey_data = 8;
    string log_id = 9;
    PublicAreaCommon public_area_common = 10;
    message MonkeyData {
        int32 score = 1;
        bool break_record = 2;
        bool need_popup = 3;
        string popup_content = 4;
    }
}
message RoomChannelGiftMessage {
    Common common = 1;
    User user = 2;
    Text rtf_content = 3;
}
message DriveGiftMessage {
    Common common = 1;
    int64 new_count = 2;
    string popup_url = 3;
}
message RoomIntroMessage {
    Common common = 1;
    User user = 2;
    int64 style = 3;
    string intro = 4;
    repeated string label = 5;
    int64 intro_video_item_id = 6;
    string intro_video_title = 7;
    repeated RoomIntroLabel selected_labels = 8;
    repeated RoomIntroLabel intro_labels = 9;
    PublicAreaCommon public_area_common = 10;
    bool poi_enabled = 11;
    RoomIntroAppointmentInfo appointment_info = 22;
}
// 抽奖
message LotteryEventMessage{
    Common common = 1;
    int64 lottery_id = 2;
    int32 lottery_status = 3;
    int64 lottery_start_time = 4;
    int64 lottery_draw_time = 5;
    int64 lottery_current_time = 6;
    string rule_page_scheme = 7;
}
message LotteryEventNewMessage{
    Common common = 1;
    int64 lottery_id = 2;
    int32 lottery_status = 3;
    int64 lottery_start_time = 4;
    int64 lottery_draw_time = 5;
    int64 lottery_current_time = 6;
    string rule_page_scheme = 7;
    int64 prize_type = 8;
    string lottery_audit_failure_reason = 9;
    repeated int32 conditions = 10;
    map<string, string> extra = 11;
    bool use_new_draw_interaction = 12;
    int64 prize_count = 13;
    int64 lucky_count = 14;
    string client_biz_data = 15;
    string server_biz_data = 16;
    string ui_config = 17;
    string award_name = 18;
    string award_description = 19;
    int64 max_random_delay_duration_by_ms = 20;
    repeated LotteryCondition conditions_detail = 21;
    string extra_award_client_biz_data = 22;
    string extra_award_server_biz_data = 23;
    string extra_award_ui_config = 24;
}
message LotteryCondition {
    int64 condition_id = 1;
    int32 type = 2;
    string content = 3;
    int32 status = 4;
    string description = 5;
    int64 gift_id = 6;
    int64 gift_count = 7;
    string gift_name = 8;
    int64 need_diamond_count = 9;
    int64 min_fans_level = 10;
    string remarks = 30;
    string biz_info = 31;
}
message NotifyEffectMessage {
    Common common = 1;
    repeated Image icons = 2;
    Text text = 3;
    Background background = 4;
    DynamicConfig dynamic_config = 5;
    CombinedText text_v2 = 6;
    bool support_landscape = 7;
    SceneConfig scene_config = 10;
    map<string, string> buried_point = 20;
    message Background {
        Image background_image = 1;
        string background_color = 10;
        Image background_effect = 11;
    }
    message DynamicConfig {
        int32 stay_time = 1;
        int32 max_stay_time = 2;
        int32 display_effect_type = 3;
    }
    message SceneConfig {
        string scene = 1;
        int64 priority = 2;
        bool need_aggregate = 3;
        int64 aggregate_num = 4;
        Text aggregate_text = 5;
        string sub_scene = 6;
        int64 max_wait_time = 7;
    }
}
//message RoomNotifyMessage{
//
//}
message ProfitGameStatusMessage{
    Common common = 1;
    int32 status = 2;
    string room_id = 3;
    StreamUrl stream_url = 4;
    int64 game_id = 5;
    string app_id = 6;
    int64 link_game_id = 7;
    StreamUrl ios_stream_url = 8;
    StreamUrl android_stream_url = 9;
    string game_extra = 10;
    int32 push_stream_type = 11;
    string linker_extra = 12;
    int64 game_type = 13;
    string init_param = 14;
    string anchor_open_id = 15;
    int32 reason = 16;
    bool server_rtc_stream = 17;
}

// npm-async-975150c9-b9518dc8.0ca6065c607a6881e9fc
// 981309
message Common {
    string method = 1;
    int64 msgId = 2;
    int64 roomId = 3;
    int64 createTime = 4;
    int32 monitor = 5;
    bool isShowMsg = 6;
    string describe = 7;
    Text displayText = 8;
    int64 foldType = 9;
    int64 anchorFoldType = 10;
    int64 priorityScore = 11;
    string logId = 12;
    string msgProcessFilterK = 13;
    string msgProcessFilterV = 14;
    string from_idc = 15;
    string to_idc = 16;
    repeated string filter_msg_tags = 17;
    LiveMessageSEI sei = 18;// LiveMessageSEI
    LiveMessageID depend_root_id = 19;
    LiveMessageID depend_id = 20;
    int64 anchor_priority_score = 21;
    int64 room_message_heat_level = 22;
    int64 fold_type_for_web = 23;
    int64 anchor_fold_type_for_web = 24;
    int64 client_send_time = 25;
    IMDispatchStrategy dispatch_strategy = 26;
    enum IMDispatchStrategy{
        IM_DISPATCH_STRATEGY_DEFAULT = 0;
        IM_DISPATCH_STRATEGY_BYPASS_DISPATCH_QUEUE = 1;
    }
}
message PublicAreaCommon {
    Image userLabel = 1;
    int64 userConsumeInRoom = 2;
    int64 userSendGiftCntInRoom = 3;
    int64 individualPriority = 4;
    map<string, string> individualStrategyResult = 5;
    int64 supportPin = 6;
    SuffixText suffixText = 7;
    int32 imAction = 8;
    bool forbiddenProfile = 9;
    ChatReplyRespInfo replyResp = 10;
    map<string, string> trackingParams = 11;
    int64 isFeatured = 12;
    bool needFilterDisplay = 13;
    PersonalizedDisplayInfo personalizedDisplayInfo = 14;
    int64 forceInsertionPriority = 15;
    ChatMentionInfo mentionInfo = 16;
    HoverInfo hoverInfo = 17;
}
message SuffixText {
    int64 bizType = 1;
    Text text = 2;
    string tags = 3;
}
message PersonalizedDisplayInfo {
    bool isPersonalized = 1;
    int64 foldType = 2;
}
message HoverInfo {
    bool isHover = 1;
    int64 priority = 2;
    int64 duration = 3;
    int64 durationInvolveDisplayTime = 4;
    int64 durationThreshold = 5;
}
message LandscapeAreaCommon{
    bool showHead = 1;
    bool showNickname = 2;
    bool showFontColor = 3;
    repeated string colorValue = 4;
    repeated int32 commentTypeTags = 5;
}
message EffectUtilImageInfo {
    string placeholderKey = 1;
    Image mixImage = 2;
}
message EffectUtilTextInfo {
    string placeholderKey = 1;
    string content = 2;
    int64 fontSize = 3;
    string fontColor = 4;
}
message ChatItem{
    int64 headSize = 1;
    int64 contentSize = 2;
    string contentColor = 3;
    FlexImageStruct background = 4;
    int64 backgroundHight = 5;
    int64 backgroundWidth = 6;
    int64 useRate = 7;
}
message FlexImageStruct{
    repeated string urlList = 1;
    string uri = 2;
    repeated int64 flexSetting = 3;
    repeated int64 textSetting = 4;
    int64 topBorderHeight = 5;
}
message EggItem{
    Image background = 1;
    int64 backgroundHight = 2;
    int64 backgroundWidth = 3;
    int64 useRate = 4;
    int64 isDynamicEgg = 5;
}
message GuestBattleUpdateContent{
    bool crownUpgrade = 1;
}
message GuestBattleFinishContent{
    int32 reason = 1;
    int64 finishUserId = 2;
    string finishOpenId = 5000;
}
message DoubleLikeDetail{
    bool doubleFlag = 1;
    int32 seqId = 2;
    int32 renewalsNum = 3;
    int32 triggersNum = 4;
}
message DisplayControlInfo{
    bool showText = 1;
    bool showIcons = 2;
}
message PicoDisplayInfo{
    int64 comboSumCount = 1;
    string emoji = 2;
    Image emojiIcon = 3;
    string emojiText = 4;
}
message ScreenChatMessageContentExt{
    string schema = 1;
    Image jumpIcon = 2;
    int32 hoverSecond = 3;
}
message EffectImageInfo{
    string placeholderKey = 1;
    Image mixImage =  2;
}
message EffectTextInfo{
    string placeholderKey = 1;
    string content = 2;
    int64 fontSize = 3;
    string fontColor = 4;
}
message RoomMsgExtra{
    RoomMsgGiftExtra RoomMsgExtra = 1;
}
message RoomMsgGiftExtra{
    int64 giftId = 1;
    string giftName = 2;
    int64 giftCount = 3;
    Image image = 4;
    Image webpImage = 5;
    int64 groupId = 6;
    int32 clientGiftSource = 7;
    string describe = 8;
    int64 diamondCount = 9;
}
message EffectiveActivityEmojiGroup{
    ActivityEmojiGroup emoji_group = 1;
    int64 start_time = 2;
    int64  end_time = 3;
}

message GiftPanelTopBar{
    string extra = 1;
    int32 topbar_action = 2;
}
message ComboTrayInfo {
    string combo_id = 1;
    Image tray_base_img = 2;
    string main_title = 3;
    string sub_title = 4;
    int32 action = 5;
    string extra = 6;
}
message GiftLiteTrayInfo{
    int64 duration_ms = 1;
}

message UnauthorizedMemberMessage{
    Common common = 1;
    int32 action = 2;
    Text nick_name_prefix = 3;
    string nick_name = 4;
    Text enter_text = 5;
    PublicAreaMessageCommon public_area_common = 6;
}
message RoomPinMessage {
    Common common = 1;
    ChatMessage chat_message = 2;
    SocialMessage social_message = 3;
    GiftMessage gift_message = 4;
    MemberMessage member_message = 5;
    LikeMessage like_message = 6;
    string method = 30;
    int64 pin_time = 31;
    User operator = 32;
    actionType action = 33;
    int64 display_duration = 34;
    int64 pin_msg_id = 35;
    string ec_streamer_key = 36;
    enum actionType {
        Unkown = 0;
        Pin = 1;
        PinCancel = 2;
    }
}
