#!/bin/bash

# 清理旧的构建文件
rm -rf dist
rm -rf release
#rm -rf node_modules

# 安装依赖
echo "Installing dependencies..."
npm install

# 确保 puppeteer 下载了 Chromium
echo "Ensuring Chromium is downloaded..."
node -e "require('puppeteer').executablePath()"

# 创建发布目录
mkdir -p release

# 构建 Linux 版本
#echo "Building Linux version..."
#npm run build:linux
#mv dist/tiktok-tools-linux release/tiktok-tools

# 构建 Windows 版本
echo "Building Windows version..."
npm run build:win
mv dist/tiktok-tools.exe release/tiktok-tools.exe

# 构建 Mac 版本
#echo "Building Mac version..."
#npm run build:mac
#mv dist/tiktok-tools-macos release/tiktok-tools-mac

# 复制必要的文件到发布目录
7z x prepare/sqlite3-win.7z -o./release/node_modules

cp ./release/tiktok-tools.exe ~/vmware/share_windows/
# 创建说明文件
cat > release/README.txt << EOL

TikTok直播监控工具

使用说明：
1. 首次运行时会自动创建配置文件 config.json
2. 可以编辑 config.json 修改配置
3. 数据文件存储在 data 目录下
4. 截图保存在 data/screenshots 目录下

配置说明：
- auth: 登录认证信息
- server: 服务器配置
- browser: 浏览器配置
- data: 数据存储配置
- monitor: 监控配置

注意：
1. 首次运行时会自动创建必要的目录和配置文件
2. Windows 用户请确保已安装 Visual C++ Redistributable
3. 如果运行时提示缺少 DLL，请安装 Visual C++ Redistributable 2015-2022
4. 请确保 node_modules 目录完整，特别是 puppeteer 和 sqlite3 相关文件
EOL

echo "Build completed! Files are in the release directory." 