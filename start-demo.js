#!/usr/bin/env node

/**
 * TikTok直播监控工具 - 界面演示启动脚本
 * 用于快速启动HTTP服务器来演示新的前端界面
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 配置
const PORT = 3002;
const PUBLIC_DIR = path.join(__dirname, 'public');

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

// 获取文件的MIME类型
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // 默认页面
    if (pathname === '/') {
        pathname = '/demo.html';
    }
    
    // 构建文件路径
    const filePath = path.join(PUBLIC_DIR, pathname);
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，返回404
            res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <title>404 - 页面未找到</title>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                        }
                        .container {
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 12px;
                            padding: 40px;
                            backdrop-filter: blur(10px);
                        }
                        h1 { font-size: 3rem; margin-bottom: 20px; }
                        p { font-size: 1.2rem; margin-bottom: 30px; }
                        a { 
                            color: #fff; 
                            text-decoration: none; 
                            background: rgba(255, 255, 255, 0.2);
                            padding: 10px 20px;
                            border-radius: 6px;
                            transition: all 0.3s ease;
                        }
                        a:hover { background: rgba(255, 255, 255, 0.3); }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>404</h1>
                        <p>页面未找到</p>
                        <a href="/">返回首页</a>
                    </div>
                </body>
                </html>
            `);
            return;
        }
        
        // 读取文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
                res.end('服务器内部错误');
                return;
            }
            
            // 设置响应头
            const mimeType = getMimeType(filePath);
            res.writeHead(200, { 
                'Content-Type': mimeType + (mimeType.startsWith('text/') ? '; charset=utf-8' : ''),
                'Cache-Control': 'no-cache'
            });
            res.end(data);
        });
    });
});

// 启动服务器
server.listen(PORT, () => {
    console.log('🎯 TikTok直播监控工具 - 界面演示服务器');
    console.log('=====================================');
    console.log(`🚀 服务器已启动: http://localhost:${PORT}`);
    console.log(`📁 静态文件目录: ${PUBLIC_DIR}`);
    console.log('');
    console.log('📖 可用页面:');
    console.log(`   • 界面演示: http://localhost:${PORT}/`);
    console.log(`   • 新版界面: http://localhost:${PORT}/index.html`);
    console.log(`   • 功能说明: http://localhost:${PORT}/README.md`);
    console.log('');
    console.log('💡 提示: 这只是界面演示服务器，不包含WebSocket功能');
    console.log('   如需完整功能，请启动完整的服务器程序');
    console.log('');
    console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n👋 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请尝试其他端口`);
    } else {
        console.error('❌ 服务器错误:', err.message);
    }
    process.exit(1);
});
