# TikTok Tools

这是一个用于 TikTok 直播数据采集和分析的工具集。

## 功能特点

- TikTok 直播数据实时采集
- 礼物数据统计和分析
- 用户互动数据记录
- 支持自定义数据导出

## 环境要求

- Node.js >= 18.19.1
- npm >= 9.2.0

## 安装

```bash
# 安装依赖
npm install
```

## 使用方法

1. 配置 `config.js` 文件，设置必要的参数
2. 运行主程序：
```bash
node main.js
```

## 打包发布

### 使用 pkg 打包

1. 安装依赖：
```bash
npm install
```

2. 执行打包：

```bash
# 打包所有支持的平台
npm run package

# 仅打包 Linux 版本
npm run package:linux

# 仅打包 Mac 版本
npm run package:mac

# 仅打包 Windows 版本
npm run package:win
```

这将在 `release` 目录下生成可执行文件，包含以下内容：
- 可执行文件（tiktok-plugin）
- 配置文件（config.js, .npmrc）
- 静态资源（public 目录）
- 数据文件（data 目录）
- proto 生成的文件（src/release_tiktok_webcast.min.js）

### 运行打包后的程序

#### Linux
```bash
# 进入release目录
cd release

# 添加执行权限
chmod +x tiktok-plugin-linux

# 运行程序
./tiktok-plugin-linux
```

#### macOS
```bash
# 进入release目录
cd release

# 添加执行权限
chmod +x tiktok-plugin-macos

# 运行程序
./tiktok-plugin-macos
```

#### Windows
```bash
# 进入release目录
cd release

# 运行程序
tiktok-plugin-win.exe
```

## 目录结构

```
.
├── src/           # 源代码目录
│   └── release_tiktok_webcast.min.js  # proto生成的文件
├── public/        # 静态资源
├── data/          # 数据文件
├── config.js      # 配置文件
├── main.js        # 主程序入口
└── package.json   # 项目配置
```

## 注意事项

1. 确保 Node.js 版本符合要求
2. 运行前请正确配置 `config.js`
3. 建议在 Linux 环境下运行
4. 需要稳定的网络连接
5. 打包后的程序不需要安装 Node.js 环境
6. 不同平台的可执行文件需要在其对应的系统上运行

## 常见问题

1. 如果遇到权限问题，请确保有适当的执行权限：
```bash
chmod +x tiktok-plugin-*
```

2. 如果遇到依赖问题，请确保所有依赖都已正确安装：
```bash
npm install protobufjs protobufjs-cli
npm install @rollup/plugin-json
npm install @rollup/plugin-terser

npm install
```

## 许可证

MIT License 