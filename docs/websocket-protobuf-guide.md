# WebSocket Protobuf 集成指南

本指南介绍如何在TikTok Tools项目中使用protobuf来优化WebSocket通信。

## 概述

我们已经将JavaScript中的WebSocket请求同步到了`proto/ws.proto`文件中，并提供了简单易用的集成方案。

## 文件结构

```
proto/
├── ws.proto                           # WebSocket消息定义
src/
├── auto_gen_webcast.js               # 自动生成的protobuf代码
├── ws-proto-example.js               # Protobuf辅助类
├── ws-proto-integration-example.js   # 集成示例
docs/
└── websocket-protobuf-guide.md       # 本文档
```

## 消息类型

### 客户端发送给服务器的消息

1. **ClientInfoMessage** - 客户端信息
2. **HeartbeatMessage** - 心跳消息
3. **LiveRoomResponseMessage** - 直播间连接响应
4. **StatusResponseMessage** - 状态响应
5. **LiveEventsMessage** - 直播事件数据
6. **ErrorMessage** - 错误消息
7. **ClientShutdownMessage** - 客户端关闭通知
8. **CustomMessage** - 自定义消息

### 服务器发送给客户端的消息

1. **WelcomeMessage** - 欢迎消息
2. **LiveRoomRequestMessage** - 直播间连接请求
3. **DisconnectRoomRequestMessage** - 断开直播间请求
4. **GetStatusRequestMessage** - 获取状态请求
5. **ServerBroadcastMessage** - 服务器广播消息
6. **ServerErrorMessage** - 服务器错误消息

## 快速开始

### 1. 生成protobuf代码

```bash
# 运行生成脚本
bash gen_sdk_proto.sh
```

### 2. 使用protobuf辅助类

```javascript
const WebSocketProtoHelper = require('./src/ws-proto-example');

const protoHelper = new WebSocketProtoHelper();

// 创建客户端信息消息
const clientInfoMessage = protoHelper.createClientInfoMessage('my-context');

// 序列化为二进制数据
const buffer = protoHelper.serializeClientMessage(clientInfoMessage);

// 通过WebSocket发送
websocket.send(buffer);
```

### 3. 集成到现有代码

```javascript
const WebSocketWithProtobuf = require('./src/ws-proto-integration-example');

// 创建WebSocket客户端（支持protobuf和JSON两种模式）
const client = new WebSocketWithProtobuf('ws://localhost:8085', 'my-client');

// 启用protobuf模式
client.setProtobufMode(true);

// 连接到服务器
client.connect();

// 发送心跳
client.sendHeartbeat(5);

// 发送直播事件
client.sendLiveEvents('conn-123', 'https://example.com/live', {
    events_data: [],
    like_total: 100,
    watching_total: 50
});
```

## 主要优势

### 1. 类型安全
- 强类型定义，避免字段名错误
- 编译时检查，减少运行时错误

### 2. 性能优化
- 二进制序列化，减少数据传输量
- 更快的序列化/反序列化速度

### 3. 向后兼容
- 支持protobuf和JSON两种模式
- 可以逐步迁移现有代码

### 4. 易于维护
- 统一的消息定义
- 自动生成的代码，减少手动维护

## 迁移指南

### 从JSON到Protobuf

**原来的JSON方式：**
```javascript
// 发送消息
websocket.send(JSON.stringify({
    type: 'heartbeat',
    context: 'my-context',
    totalConnections: 5,
    timestamp: Date.now()
}));

// 接收消息
websocket.on('message', (data) => {
    const message = JSON.parse(data);
    if (message.type === 'heartbeat') {
        // 处理心跳消息
    }
});
```

**新的Protobuf方式：**
```javascript
// 发送消息
const message = protoHelper.createHeartbeatMessage('my-context', 5);
const buffer = protoHelper.serializeClientMessage(message);
websocket.send(buffer);

// 接收消息
websocket.on('message', (buffer) => {
    const message = protoHelper.deserializeServerMessage(buffer);
    const messageType = protoHelper.getMessageType(message);
    if (messageType === 'heartbeat') {
        // 处理心跳消息
    }
});
```

## 配置选项

### 切换protobuf/JSON模式

```javascript
// 启用protobuf模式
client.setProtobufMode(true);

// 禁用protobuf模式（使用JSON）
client.setProtobufMode(false);
```

### 自定义消息处理

```javascript
class MyWebSocketClient extends WebSocketWithProtobuf {
    handleLiveRoomRequest(message) {
        console.log('Custom handling:', message.liveUrl);
        // 自定义处理逻辑
        super.handleLiveRoomRequest(message);
    }
}
```

## 最佳实践

### 1. 错误处理
```javascript
try {
    const buffer = protoHelper.serializeClientMessage(message);
    websocket.send(buffer);
} catch (error) {
    console.error('Protobuf serialization error:', error);
    // 降级到JSON模式
    websocket.send(JSON.stringify(fallbackData));
}
```

### 2. 消息验证
```javascript
// 在发送前验证消息
const message = protoHelper.createHeartbeatMessage(context, connections);
if (protoHelper.validateMessage(message)) {
    const buffer = protoHelper.serializeClientMessage(message);
    websocket.send(buffer);
}
```

### 3. 性能监控
```javascript
// 监控序列化性能
const start = Date.now();
const buffer = protoHelper.serializeClientMessage(message);
const serializationTime = Date.now() - start;
console.log(`Serialization took ${serializationTime}ms`);
```

## 故障排除

### 常见问题

1. **protobuf生成失败**
   - 检查`proto/ws.proto`语法
   - 确保安装了必要的依赖

2. **消息解析错误**
   - 确认客户端和服务器使用相同的proto定义
   - 检查消息版本兼容性

3. **性能问题**
   - 对于小消息，JSON可能更快
   - 对于大消息或高频消息，protobuf更优

### 调试技巧

```javascript
// 启用详细日志
const message = protoHelper.deserializeServerMessage(buffer);
console.log('Received message:', JSON.stringify(message.toJSON(), null, 2));

// 比较protobuf和JSON大小
const jsonSize = JSON.stringify(data).length;
const protobufSize = buffer.length;
console.log(`Size comparison - JSON: ${jsonSize}, Protobuf: ${protobufSize}`);
```

## 测试验证

运行测试脚本验证protobuf功能：

```bash
# 运行protobuf功能测试
node test/ws-proto-test.js
```

测试包括：
- ✅ 所有消息类型的创建和序列化
- ✅ 消息的反序列化和验证
- ✅ 消息类型识别
- ✅ 性能对比测试
- ✅ 错误处理测试

## 集成完成状态

### ✅ 已完成
1. **protobuf定义** - `proto/ws.proto` 包含所有WebSocket消息类型
2. **代码生成** - 自动生成的JavaScript protobuf代码
3. **辅助类** - `WebSocketProtoHelper` 提供简单易用的API
4. **集成示例** - `WebSocketWithProtobuf` 展示如何集成到现有代码
5. **测试验证** - 完整的测试套件验证所有功能
6. **文档说明** - 详细的使用指南和最佳实践

### 🔄 可选的下一步
1. 在现有的`live-websocket-bridge.js`中集成protobuf支持
2. 更新服务器端代码以支持protobuf
3. 添加消息压缩支持（gzip）
4. 实现消息重试和确认机制
5. 添加消息版本控制

## 使用建议

### 何时使用protobuf
- ✅ 高频消息传输（如直播事件）
- ✅ 大量数据传输
- ✅ 需要类型安全的场景
- ✅ 多语言客户端支持

### 何时使用JSON
- ✅ 简单的控制消息
- ✅ 调试和开发阶段
- ✅ 与不支持protobuf的系统集成
- ✅ 消息体很小的场景

## 参考资料

- [Protocol Buffers 官方文档](https://developers.google.com/protocol-buffers)
- [protobuf.js 文档](https://github.com/protobufjs/protobuf.js)
- [WebSocket API 文档](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
